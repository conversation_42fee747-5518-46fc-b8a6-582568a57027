<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>mainWindow</class>
 <widget class="QMainWindow" name="mainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1186</width>
    <height>913</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>孟剪工具</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QTabWidget" name="HJ_tab">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>20</y>
      <width>1171</width>
      <height>841</height>
     </rect>
    </property>
    <property name="minimumSize">
     <size>
      <width>0</width>
      <height>841</height>
     </size>
    </property>
    <property name="styleSheet">
     <string notr="true">/* 标签页基本样式（所有标签） */
QTabBar::tab {
    height: 30px;        /* 标签页高度（可改） */
    min-width: 100px;    /* 标签页最小宽度（可改） */
    padding: 8px 15px;   /* 内边距（上下 左右，可改） */
    border: 1px solid #ccc;  /* 边框（可选） */
    border-bottom: none;     /* 底部无边框（与内容区分） */
    border-radius: 4px 4px 0 0;  /* 顶部圆角（可选） */
    background: #ffffff;    /* 背景色（可选） */
    font-size: 14px;        /* 字体大小（可选） */
}

/* 选中标签页的样式（可选） */
QTabBar::tab:selected {
    background: #48fba1;    /* 选中时背景色（可改） */
    font-weight: bold;      /* 选中时字体加粗（可选） */
}</string>
    </property>
    <property name="currentIndex">
     <number>0</number>
    </property>
    <widget class="QWidget" name="tab_QT">
     <property name="font">
      <font>
       <bold>false</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">/* 设置标签页的整体样式 */
QTabBar::tab {
    height: 50px;        /* 标签页高度 */
    min-width: 80px;     /* 标签页最小宽度 */
    padding: 5px 10px;   /* 内边距（上下 左右） */
}</string>
     </property>
     <attribute name="title">
      <string>前后贴处理工具</string>
     </attribute>
     <widget class="QGroupBox" name="SPZD_groupBox">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>10</y>
        <width>691</width>
        <height>411</height>
       </rect>
      </property>
      <property name="title">
       <string>视频指定区</string>
      </property>
      <widget class="QListView" name="QT_listView">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>40</y>
         <width>331</width>
         <height>231</height>
        </rect>
       </property>
      </widget>
      <widget class="QLabel" name="QT_name">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>20</y>
         <width>91</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string>前贴素材列表</string>
       </property>
      </widget>
      <widget class="QPushButton" name="QT_in">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>280</y>
         <width>111</width>
         <height>41</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="text">
        <string>添加前贴文件</string>
       </property>
      </widget>
      <widget class="QPushButton" name="QT_out">
       <property name="geometry">
        <rect>
         <x>120</x>
         <y>280</y>
         <width>111</width>
         <height>41</height>
        </rect>
       </property>
       <property name="text">
        <string>移除选中</string>
       </property>
      </widget>
      <widget class="QPushButton" name="QT_clean">
       <property name="geometry">
        <rect>
         <x>230</x>
         <y>280</y>
         <width>111</width>
         <height>41</height>
        </rect>
       </property>
       <property name="text">
        <string>清空列表</string>
       </property>
      </widget>
      <widget class="QLabel" name="HT_name_2">
       <property name="geometry">
        <rect>
         <x>360</x>
         <y>20</y>
         <width>91</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string>后贴素材列表</string>
       </property>
      </widget>
      <widget class="QPushButton" name="HT_out">
       <property name="geometry">
        <rect>
         <x>460</x>
         <y>280</y>
         <width>111</width>
         <height>41</height>
        </rect>
       </property>
       <property name="text">
        <string>移除选中</string>
       </property>
      </widget>
      <widget class="QPushButton" name="HT_clean">
       <property name="geometry">
        <rect>
         <x>570</x>
         <y>280</y>
         <width>111</width>
         <height>41</height>
        </rect>
       </property>
       <property name="text">
        <string>清空列表</string>
       </property>
      </widget>
      <widget class="QPushButton" name="HT_in">
       <property name="geometry">
        <rect>
         <x>350</x>
         <y>280</y>
         <width>111</width>
         <height>41</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="text">
        <string>添加后贴文件</string>
       </property>
      </widget>
      <widget class="QListView" name="HT_listView_2">
       <property name="geometry">
        <rect>
         <x>350</x>
         <y>40</y>
         <width>331</width>
         <height>231</height>
        </rect>
       </property>
      </widget>
      <widget class="QLabel" name="SC_name">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>340</y>
         <width>91</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string>输出目录：</string>
       </property>
      </widget>
      <widget class="QLineEdit" name="SC_lineEdit">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>360</y>
         <width>271</width>
         <height>31</height>
        </rect>
       </property>
      </widget>
      <widget class="QPushButton" name="SC_where">
       <property name="geometry">
        <rect>
         <x>300</x>
         <y>360</y>
         <width>111</width>
         <height>31</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>235</green>
             <blue>15</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Light">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Midlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>235</green>
             <blue>15</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Light">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Midlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>235</green>
             <blue>15</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Light">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Midlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="text">
        <string>选择输出目录</string>
       </property>
      </widget>
      <widget class="QPushButton" name="CSHJC">
       <property name="geometry">
        <rect>
         <x>500</x>
         <y>350</y>
         <width>141</width>
         <height>41</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>0</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Light">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Midlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>0</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Light">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Midlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>0</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Light">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Midlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="font">
        <font>
         <pointsize>11</pointsize>
         <bold>false</bold>
        </font>
       </property>
       <property name="text">
        <string>初始化检查</string>
       </property>
      </widget>
     </widget>
     <widget class="QGroupBox" name="KZ_groupBox">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>430</y>
        <width>691</width>
        <height>331</height>
       </rect>
      </property>
      <property name="title">
       <string>扩展功能</string>
      </property>
      <widget class="QCheckBox" name="CZ_checkBox">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>30</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>抽帧去重</string>
       </property>
      </widget>
      <widget class="QLabel" name="CZ_label">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>110</y>
         <width>71</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>最小抽帧数：</string>
       </property>
      </widget>
      <widget class="QLabel" name="CZ_label_2">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>190</y>
         <width>71</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>最大抽帧数：</string>
       </property>
      </widget>
      <widget class="Line" name="line_2">
       <property name="geometry">
        <rect>
         <x>130</x>
         <y>30</y>
         <width>20</width>
         <height>281</height>
        </rect>
       </property>
       <property name="orientation">
        <enum>Qt::Orientation::Vertical</enum>
       </property>
      </widget>
      <widget class="QCheckBox" name="ZC_checkBox">
       <property name="geometry">
        <rect>
         <x>160</x>
         <y>30</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>加转场</string>
       </property>
      </widget>
      <widget class="QSpinBox" name="CZ_spinBox_low">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>130</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="minimum">
        <number>1</number>
       </property>
       <property name="value">
        <number>15</number>
       </property>
      </widget>
      <widget class="QSpinBox" name="CZ_spinBox_max">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>220</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="minimum">
        <number>2</number>
       </property>
       <property name="value">
        <number>50</number>
       </property>
      </widget>
      <widget class="Line" name="line_3">
       <property name="geometry">
        <rect>
         <x>510</x>
         <y>30</y>
         <width>20</width>
         <height>281</height>
        </rect>
       </property>
       <property name="orientation">
        <enum>Qt::Orientation::Vertical</enum>
       </property>
      </widget>
      <widget class="QCheckBox" name="CMM_checkBox_2">
       <property name="geometry">
        <rect>
         <x>540</x>
         <y>30</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>批量重命名</string>
       </property>
      </widget>
      <widget class="QLabel" name="CMM_label">
       <property name="geometry">
        <rect>
         <x>530</x>
         <y>70</y>
         <width>71</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>起始值：</string>
       </property>
      </widget>
      <widget class="QSpinBox" name="CMM_spinBox">
       <property name="geometry">
        <rect>
         <x>530</x>
         <y>90</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="minimum">
        <number>1</number>
       </property>
       <property name="maximum">
        <number>99999</number>
       </property>
      </widget>
      <widget class="QLabel" name="CMM_label_2">
       <property name="geometry">
        <rect>
         <x>530</x>
         <y>130</y>
         <width>71</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>名称前缀：</string>
       </property>
      </widget>
      <widget class="QLineEdit" name="CMM_lineEdit">
       <property name="geometry">
        <rect>
         <x>530</x>
         <y>150</y>
         <width>121</width>
         <height>31</height>
        </rect>
       </property>
      </widget>
      <widget class="QLabel" name="CMM_label_3">
       <property name="geometry">
        <rect>
         <x>530</x>
         <y>190</y>
         <width>71</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>名称后缀：</string>
       </property>
      </widget>
      <widget class="QLineEdit" name="CMM_lineEdit_2">
       <property name="geometry">
        <rect>
         <x>530</x>
         <y>210</y>
         <width>121</width>
         <height>31</height>
        </rect>
       </property>
      </widget>
      <widget class="QCheckBox" name="CMM_checkBox_3">
       <property name="geometry">
        <rect>
         <x>530</x>
         <y>250</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="text">
        <string>填充0来补足长度</string>
       </property>
       <property name="checked">
        <bool>true</bool>
       </property>
       <property name="tristate">
        <bool>false</bool>
       </property>
      </widget>
      <widget class="QSpinBox" name="spinBox">
       <property name="geometry">
        <rect>
         <x>560</x>
         <y>280</y>
         <width>71</width>
         <height>23</height>
        </rect>
       </property>
       <property name="value">
        <number>2</number>
       </property>
      </widget>
      <widget class="Line" name="line_4">
       <property name="geometry">
        <rect>
         <x>150</x>
         <y>136</y>
         <width>351</width>
         <height>20</height>
        </rect>
       </property>
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
      </widget>
      <widget class="QCheckBox" name="YY_checkBox_2">
       <property name="geometry">
        <rect>
         <x>160</x>
         <y>160</y>
         <width>151</width>
         <height>31</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>整体添加背景音乐</string>
       </property>
      </widget>
      <widget class="QPushButton" name="YY_pushButton">
       <property name="geometry">
        <rect>
         <x>400</x>
         <y>200</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="text">
        <string>选择音乐目录</string>
       </property>
      </widget>
      <widget class="QLineEdit" name="YY_lineEdit">
       <property name="geometry">
        <rect>
         <x>160</x>
         <y>200</y>
         <width>231</width>
         <height>31</height>
        </rect>
       </property>
      </widget>
      <widget class="QLabel" name="YY_label">
       <property name="geometry">
        <rect>
         <x>160</x>
         <y>250</y>
         <width>71</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>原视频音量：</string>
       </property>
      </widget>
      <widget class="QLabel" name="YY_label_2">
       <property name="geometry">
        <rect>
         <x>320</x>
         <y>250</y>
         <width>71</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>音乐音量：</string>
       </property>
      </widget>
      <widget class="QSpinBox" name="YY_spinBox_yspyl">
       <property name="geometry">
        <rect>
         <x>160</x>
         <y>280</y>
         <width>81</width>
         <height>31</height>
        </rect>
       </property>
       <property name="minimum">
        <number>-30</number>
       </property>
       <property name="maximum">
        <number>15</number>
       </property>
       <property name="value">
        <number>-5</number>
       </property>
      </widget>
      <widget class="QSpinBox" name="YY_spinBox_yyyl">
       <property name="geometry">
        <rect>
         <x>320</x>
         <y>280</y>
         <width>81</width>
         <height>31</height>
        </rect>
       </property>
       <property name="minimum">
        <number>-30</number>
       </property>
       <property name="maximum">
        <number>15</number>
       </property>
       <property name="value">
        <number>-10</number>
       </property>
      </widget>
      <widget class="QLabel" name="YY_label_3">
       <property name="geometry">
        <rect>
         <x>250</x>
         <y>290</y>
         <width>41</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>dB</string>
       </property>
      </widget>
      <widget class="QLabel" name="YY_label_4">
       <property name="geometry">
        <rect>
         <x>410</x>
         <y>290</y>
         <width>41</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>dB</string>
       </property>
      </widget>
      <widget class="QLabel" name="YY_label_5">
       <property name="geometry">
        <rect>
         <x>440</x>
         <y>250</y>
         <width>61</width>
         <height>71</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="WindowText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>254</red>
             <green>0</green>
             <blue>4</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Text">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="ToolTipText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="WindowText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>254</red>
             <green>0</green>
             <blue>4</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Text">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="ToolTipText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="ToolTipText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="contextMenuPolicy">
        <enum>Qt::ContextMenuPolicy::ActionsContextMenu</enum>
       </property>
       <property name="text">
        <string>注：音量范围为-30dB到15dB</string>
       </property>
       <property name="wordWrap">
        <bool>true</bool>
       </property>
      </widget>
      <widget class="QComboBox" name="ZC_comboBox">
       <property name="geometry">
        <rect>
         <x>170</x>
         <y>70</y>
         <width>231</width>
         <height>41</height>
        </rect>
       </property>
       <item>
        <property name="text">
         <string>【【全随机】】</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【【柔 · 随机】】</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【【硬 · 随机】】</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【叠化】fade</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向左擦除】wipeleft</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向右擦除】wiperight</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向上擦除】wipeup</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向下擦除】wipedown</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【向左擦除】smoothleft</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【向右擦除】smoothright</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【向上擦除】smoothup</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【向下擦除】smoothdown</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向左滑动】slideleft</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向右滑动】slideright</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向上滑动】slideup</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向下滑动】slidedown</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【圆形展开】circleopen</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【圆形闭合】circleclose</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【垂直展开】vertopen</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【垂直闭合】vertclose</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【水平展开】horzopen</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【水平闭合】horzclose</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【景深转场】distance</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【时钟擦除】radial</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【像素模糊】pixelize</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【放大转场】zoomin</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【向左上擦除】diagtl</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【向右上擦除】diagtr</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【向左下擦除】diagbl</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【向右下擦除】diagbr</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向左上擦除】wipetl</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向右上擦除】wipetr</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向左下擦除】wipebl</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向右下擦除】wipebr</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向左百叶窗】hlslice</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向右百叶窗】hrslice</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向上百叶窗】vuslice</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向下百叶窗】vdslice</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向左滑刺】hlwind</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向右滑刺】hrwind</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向上滑刺】vuwind</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向下滑刺】vdwind</string>
        </property>
       </item>
      </widget>
      <widget class="QLabel" name="ZC_label_10">
       <property name="geometry">
        <rect>
         <x>420</x>
         <y>50</y>
         <width>71</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>转场时长</string>
       </property>
      </widget>
      <widget class="QDoubleSpinBox" name="ZC_doubleSpinBox">
       <property name="geometry">
        <rect>
         <x>420</x>
         <y>80</y>
         <width>81</width>
         <height>23</height>
        </rect>
       </property>
       <property name="decimals">
        <number>1</number>
       </property>
       <property name="minimum">
        <double>0.500000000000000</double>
       </property>
       <property name="maximum">
        <double>3.000000000000000</double>
       </property>
      </widget>
     </widget>
     <widget class="QGroupBox" name="HX_groupBox">
      <property name="geometry">
       <rect>
        <x>720</x>
        <y>10</y>
        <width>421</width>
        <height>751</height>
       </rect>
      </property>
      <property name="title">
       <string>核心处理</string>
      </property>
      <widget class="QLabel" name="CL_label">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>30</y>
         <width>81</width>
         <height>16</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>false</bold>
        </font>
       </property>
       <property name="text">
        <string>处理日志</string>
       </property>
      </widget>
      <widget class="QPlainTextEdit" name="CL_plainTextEdit">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>50</y>
         <width>381</width>
         <height>311</height>
        </rect>
       </property>
      </widget>
      <widget class="Line" name="line">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>370</y>
         <width>381</width>
         <height>16</height>
        </rect>
       </property>
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
      </widget>
      <widget class="QWidget" name="CL_widget" native="true">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>410</y>
         <width>381</width>
         <height>231</height>
        </rect>
       </property>
      </widget>
      <widget class="QLabel" name="CL_label_2">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>390</y>
         <width>71</width>
         <height>16</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>false</bold>
        </font>
       </property>
       <property name="text">
        <string>策略选择</string>
       </property>
      </widget>
      <widget class="QPushButton" name="KS_pushButton">
       <property name="geometry">
        <rect>
         <x>100</x>
         <y>700</y>
         <width>141</width>
         <height>41</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>0</red>
             <green>255</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>0</red>
             <green>255</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>0</red>
             <green>255</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>让我们嗨起来好嘛</string>
       </property>
      </widget>
      <widget class="QPushButton" name="TZ_pushButton">
       <property name="geometry">
        <rect>
         <x>260</x>
         <y>700</y>
         <width>131</width>
         <height>41</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>0</green>
             <blue>4</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>0</green>
             <blue>4</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>0</green>
             <blue>4</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>停下来 停下来</string>
       </property>
      </widget>
      <widget class="QProgressBar" name="progressBar">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>660</y>
         <width>381</width>
         <height>23</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <bold>true</bold>
        </font>
       </property>
       <property name="value">
        <number>0</number>
       </property>
      </widget>
     </widget>
    </widget>
    <widget class="QWidget" name="tab_CZ">
     <attribute name="title">
      <string>单独抽帧工具</string>
     </attribute>
     <widget class="QPushButton" name="DDCZ_pushButton_2">
      <property name="geometry">
       <rect>
        <x>830</x>
        <y>730</y>
        <width>141</width>
        <height>41</height>
       </rect>
      </property>
      <property name="palette">
       <palette>
        <active>
         <colorrole role="Button">
          <brush brushstyle="SolidPattern">
           <color alpha="179">
            <red>0</red>
            <green>255</green>
            <blue>255</blue>
           </color>
          </brush>
         </colorrole>
        </active>
        <inactive>
         <colorrole role="Button">
          <brush brushstyle="SolidPattern">
           <color alpha="179">
            <red>0</red>
            <green>255</green>
            <blue>255</blue>
           </color>
          </brush>
         </colorrole>
        </inactive>
        <disabled>
         <colorrole role="Button">
          <brush brushstyle="SolidPattern">
           <color alpha="179">
            <red>0</red>
            <green>255</green>
            <blue>255</blue>
           </color>
          </brush>
         </colorrole>
        </disabled>
       </palette>
      </property>
      <property name="font">
       <font>
        <pointsize>10</pointsize>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>开始吧</string>
      </property>
     </widget>
     <widget class="QProgressBar" name="DDCZprogressBar_2">
      <property name="geometry">
       <rect>
        <x>750</x>
        <y>690</y>
        <width>381</width>
        <height>23</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <bold>true</bold>
       </font>
      </property>
      <property name="value">
       <number>0</number>
      </property>
     </widget>
     <widget class="QPushButton" name="DDCZ_pushButton_3">
      <property name="geometry">
       <rect>
        <x>990</x>
        <y>730</y>
        <width>131</width>
        <height>41</height>
       </rect>
      </property>
      <property name="palette">
       <palette>
        <active>
         <colorrole role="Button">
          <brush brushstyle="SolidPattern">
           <color alpha="179">
            <red>255</red>
            <green>0</green>
            <blue>4</blue>
           </color>
          </brush>
         </colorrole>
        </active>
        <inactive>
         <colorrole role="Button">
          <brush brushstyle="SolidPattern">
           <color alpha="179">
            <red>255</red>
            <green>0</green>
            <blue>4</blue>
           </color>
          </brush>
         </colorrole>
        </inactive>
        <disabled>
         <colorrole role="Button">
          <brush brushstyle="SolidPattern">
           <color alpha="179">
            <red>255</red>
            <green>0</green>
            <blue>4</blue>
           </color>
          </brush>
         </colorrole>
        </disabled>
       </palette>
      </property>
      <property name="font">
       <font>
        <pointsize>10</pointsize>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>停下来 停下来</string>
      </property>
     </widget>
     <widget class="QGroupBox" name="DDCZ_groupBox_KZ">
      <property name="geometry">
       <rect>
        <x>670</x>
        <y>30</y>
        <width>451</width>
        <height>261</height>
       </rect>
      </property>
      <property name="title">
       <string>扩展工具</string>
      </property>
      <widget class="QSpinBox" name="spinBox_2">
       <property name="geometry">
        <rect>
         <x>250</x>
         <y>200</y>
         <width>71</width>
         <height>23</height>
        </rect>
       </property>
       <property name="value">
        <number>2</number>
       </property>
      </widget>
      <widget class="QCheckBox" name="CMM_checkBox_5">
       <property name="geometry">
        <rect>
         <x>220</x>
         <y>170</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="text">
        <string>填充0来补足长度</string>
       </property>
       <property name="checked">
        <bool>true</bool>
       </property>
       <property name="tristate">
        <bool>false</bool>
       </property>
      </widget>
      <widget class="QLabel" name="CMM_label_6">
       <property name="geometry">
        <rect>
         <x>210</x>
         <y>100</y>
         <width>71</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>名称后缀：</string>
       </property>
      </widget>
      <widget class="QLineEdit" name="CMM_lineEdit_3">
       <property name="geometry">
        <rect>
         <x>210</x>
         <y>60</y>
         <width>151</width>
         <height>31</height>
        </rect>
       </property>
      </widget>
      <widget class="QSpinBox" name="CMM_spinBox_2">
       <property name="geometry">
        <rect>
         <x>40</x>
         <y>100</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="minimum">
        <number>1</number>
       </property>
       <property name="maximum">
        <number>99999</number>
       </property>
      </widget>
      <widget class="QCheckBox" name="CMM_checkBox_4">
       <property name="geometry">
        <rect>
         <x>40</x>
         <y>40</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>批量重命名</string>
       </property>
      </widget>
      <widget class="QLineEdit" name="CMM_lineEdit_4">
       <property name="geometry">
        <rect>
         <x>210</x>
         <y>120</y>
         <width>151</width>
         <height>31</height>
        </rect>
       </property>
      </widget>
      <widget class="QLabel" name="CMM_label_4">
       <property name="geometry">
        <rect>
         <x>210</x>
         <y>40</y>
         <width>71</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>名称前缀：</string>
       </property>
      </widget>
      <widget class="QLabel" name="CMM_label_5">
       <property name="geometry">
        <rect>
         <x>40</x>
         <y>80</y>
         <width>71</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>起始值：</string>
       </property>
      </widget>
     </widget>
     <widget class="QGroupBox" name="DDCZ_groupBox">
      <property name="geometry">
       <rect>
        <x>40</x>
        <y>30</y>
        <width>591</width>
        <height>731</height>
       </rect>
      </property>
      <property name="title">
       <string>抽帧设定区</string>
      </property>
      <widget class="QSpinBox" name="DDCZ_spinBox_low_2">
       <property name="geometry">
        <rect>
         <x>150</x>
         <y>660</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="minimum">
        <number>1</number>
       </property>
       <property name="value">
        <number>15</number>
       </property>
      </widget>
      <widget class="QSpinBox" name="DDCZ_spinBox_max_2">
       <property name="geometry">
        <rect>
         <x>320</x>
         <y>660</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="minimum">
        <number>2</number>
       </property>
       <property name="value">
        <number>50</number>
       </property>
      </widget>
      <widget class="QLabel" name="DDCZ_name_3">
       <property name="geometry">
        <rect>
         <x>50</x>
         <y>40</y>
         <width>91</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string>单独抽帧文件</string>
       </property>
      </widget>
      <widget class="QLabel" name="DDCZ_label_3">
       <property name="geometry">
        <rect>
         <x>150</x>
         <y>630</y>
         <width>71</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>最小抽帧数：</string>
       </property>
      </widget>
      <widget class="QListView" name="DDCZ_listView_3">
       <property name="geometry">
        <rect>
         <x>40</x>
         <y>60</y>
         <width>361</width>
         <height>271</height>
        </rect>
       </property>
      </widget>
      <widget class="QPushButton" name="DDCZ_in_3">
       <property name="geometry">
        <rect>
         <x>50</x>
         <y>350</y>
         <width>111</width>
         <height>41</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="text">
        <string>添加文件</string>
       </property>
      </widget>
      <widget class="QLabel" name="DDCZ_label_4">
       <property name="geometry">
        <rect>
         <x>320</x>
         <y>630</y>
         <width>71</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>最大抽帧数：</string>
       </property>
      </widget>
      <widget class="QPushButton" name="DDCZ_clean_3">
       <property name="geometry">
        <rect>
         <x>270</x>
         <y>350</y>
         <width>111</width>
         <height>41</height>
        </rect>
       </property>
       <property name="text">
        <string>清空列表</string>
       </property>
      </widget>
      <widget class="QPushButton" name="DDCZ_out_3">
       <property name="geometry">
        <rect>
         <x>160</x>
         <y>350</y>
         <width>111</width>
         <height>41</height>
        </rect>
       </property>
       <property name="text">
        <string>移除选中</string>
       </property>
      </widget>
      <widget class="QRadioButton" name="DDCZ_radioButton_TH">
       <property name="geometry">
        <rect>
         <x>60</x>
         <y>450</y>
         <width>98</width>
         <height>19</height>
        </rect>
       </property>
       <property name="text">
        <string>替换原素材</string>
       </property>
      </widget>
      <widget class="QRadioButton" name="DDCZ_radioButton_ZD">
       <property name="geometry">
        <rect>
         <x>220</x>
         <y>450</y>
         <width>98</width>
         <height>19</height>
        </rect>
       </property>
       <property name="text">
        <string>指定输出目录</string>
       </property>
       <property name="checked">
        <bool>true</bool>
       </property>
      </widget>
      <widget class="QWidget" name="DDCZ_widget_SC" native="true">
       <property name="geometry">
        <rect>
         <x>40</x>
         <y>500</y>
         <width>501</width>
         <height>81</height>
        </rect>
       </property>
       <widget class="QPushButton" name="DDCZ_where_3">
        <property name="geometry">
         <rect>
          <x>380</x>
          <y>30</y>
          <width>111</width>
          <height>31</height>
         </rect>
        </property>
        <property name="palette">
         <palette>
          <active>
           <colorrole role="Button">
            <brush brushstyle="SolidPattern">
             <color alpha="179">
              <red>255</red>
              <green>235</green>
              <blue>15</blue>
             </color>
            </brush>
           </colorrole>
           <colorrole role="Light">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>255</green>
              <blue>127</blue>
             </color>
            </brush>
           </colorrole>
           <colorrole role="Midlight">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>255</green>
              <blue>127</blue>
             </color>
            </brush>
           </colorrole>
          </active>
          <inactive>
           <colorrole role="Button">
            <brush brushstyle="SolidPattern">
             <color alpha="179">
              <red>255</red>
              <green>235</green>
              <blue>15</blue>
             </color>
            </brush>
           </colorrole>
           <colorrole role="Light">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>255</green>
              <blue>127</blue>
             </color>
            </brush>
           </colorrole>
           <colorrole role="Midlight">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>255</green>
              <blue>127</blue>
             </color>
            </brush>
           </colorrole>
          </inactive>
          <disabled>
           <colorrole role="Button">
            <brush brushstyle="SolidPattern">
             <color alpha="179">
              <red>255</red>
              <green>235</green>
              <blue>15</blue>
             </color>
            </brush>
           </colorrole>
           <colorrole role="Light">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>255</green>
              <blue>127</blue>
             </color>
            </brush>
           </colorrole>
           <colorrole role="Midlight">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>255</green>
              <blue>127</blue>
             </color>
            </brush>
           </colorrole>
          </disabled>
         </palette>
        </property>
        <property name="text">
         <string>选择输出目录</string>
        </property>
       </widget>
       <widget class="QLabel" name="DDCZ_outname_3">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>10</y>
          <width>91</width>
          <height>16</height>
         </rect>
        </property>
        <property name="text">
         <string>输出目录：</string>
        </property>
       </widget>
       <widget class="QLineEdit" name="DDCZ_lineEdit_3">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>30</y>
          <width>321</width>
          <height>31</height>
         </rect>
        </property>
       </widget>
      </widget>
     </widget>
     <widget class="QPlainTextEdit" name="DDCZ_plainTextEdit_2">
      <property name="geometry">
       <rect>
        <x>670</x>
        <y>340</y>
        <width>451</width>
        <height>311</height>
       </rect>
      </property>
     </widget>
     <widget class="QLabel" name="DDCZ_label_5">
      <property name="geometry">
       <rect>
        <x>670</x>
        <y>320</y>
        <width>81</width>
        <height>16</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>10</pointsize>
        <bold>false</bold>
       </font>
      </property>
      <property name="text">
       <string>处理日志</string>
      </property>
     </widget>
    </widget>
    <widget class="QWidget" name="tab_HJ">
     <attribute name="title">
      <string>混剪工具</string>
     </attribute>
     <widget class="QGroupBox" name="HJSPZD_groupBox_2">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>20</y>
        <width>491</width>
        <height>741</height>
       </rect>
      </property>
      <property name="title">
       <string>视频指定区</string>
      </property>
      <widget class="QListView" name="HJ_listView_4">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>40</y>
         <width>471</width>
         <height>211</height>
        </rect>
       </property>
      </widget>
      <widget class="QLabel" name="HJ_name_4">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>20</y>
         <width>91</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string>素材列表</string>
       </property>
      </widget>
      <widget class="QPushButton" name="HJ_in_4">
       <property name="geometry">
        <rect>
         <x>100</x>
         <y>270</y>
         <width>111</width>
         <height>41</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="text">
        <string>添加视频文件</string>
       </property>
      </widget>
      <widget class="QPushButton" name="HJ_out_4">
       <property name="geometry">
        <rect>
         <x>210</x>
         <y>270</y>
         <width>111</width>
         <height>41</height>
        </rect>
       </property>
       <property name="text">
        <string>移除选中</string>
       </property>
      </widget>
      <widget class="QPushButton" name="HJ_clean_4">
       <property name="geometry">
        <rect>
         <x>320</x>
         <y>270</y>
         <width>111</width>
         <height>41</height>
        </rect>
       </property>
       <property name="text">
        <string>清空列表</string>
       </property>
      </widget>
      <widget class="QLabel" name="HJSC_name_4">
       <property name="geometry">
        <rect>
         <x>40</x>
         <y>340</y>
         <width>91</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string>输出目录：</string>
       </property>
      </widget>
      <widget class="QLineEdit" name="HJSC_lineEdit_4">
       <property name="geometry">
        <rect>
         <x>40</x>
         <y>360</y>
         <width>271</width>
         <height>31</height>
        </rect>
       </property>
      </widget>
      <widget class="QPushButton" name="HJSC_where_4">
       <property name="geometry">
        <rect>
         <x>320</x>
         <y>360</y>
         <width>111</width>
         <height>31</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>235</green>
             <blue>15</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Light">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Midlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>235</green>
             <blue>15</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Light">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Midlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>235</green>
             <blue>15</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Light">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Midlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="text">
        <string>选择输出目录</string>
       </property>
      </widget>
      <widget class="QPushButton" name="CSHJC_4">
       <property name="geometry">
        <rect>
         <x>320</x>
         <y>680</y>
         <width>141</width>
         <height>41</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>0</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Light">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Midlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>0</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Light">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Midlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>0</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Light">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Midlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="font">
        <font>
         <pointsize>11</pointsize>
         <bold>false</bold>
        </font>
       </property>
       <property name="text">
        <string>初始化检查</string>
       </property>
      </widget>
      <widget class="QSpinBox" name="HJ_spinBox_3">
       <property name="geometry">
        <rect>
         <x>170</x>
         <y>680</y>
         <width>91</width>
         <height>41</height>
        </rect>
       </property>
       <property name="minimum">
        <number>1</number>
       </property>
       <property name="maximum">
        <number>9999</number>
       </property>
      </widget>
      <widget class="QLabel" name="HJ_label">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>690</y>
         <width>131</width>
         <height>31</height>
        </rect>
       </property>
       <property name="text">
        <string>希望生成的视频数量</string>
       </property>
      </widget>
      <widget class="QPushButton" name="HJ_pushButton_GJ">
       <property name="geometry">
        <rect>
         <x>30</x>
         <y>420</y>
         <width>111</width>
         <height>31</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="WindowText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>170</red>
             <green>170</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="0">
             <red>255</red>
             <green>255</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Text">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>170</red>
             <green>0</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="WindowText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>170</red>
             <green>170</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="0">
             <red>255</red>
             <green>255</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Text">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>170</red>
             <green>0</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="0">
             <red>255</red>
             <green>255</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="text">
        <string>高级设置 ▼</string>
       </property>
      </widget>
      <widget class="QLabel" name="HJ_label_GJ">
       <property name="geometry">
        <rect>
         <x>160</x>
         <y>420</y>
         <width>311</width>
         <height>31</height>
        </rect>
       </property>
       <property name="text">
        <string>基础设定为：片段长度3~5秒，目标视频总时长15-31秒
单素材仅使用一次，素材声音保留</string>
       </property>
       <property name="wordWrap">
        <bool>false</bool>
       </property>
       <property name="openExternalLinks">
        <bool>false</bool>
       </property>
      </widget>
      <widget class="QGroupBox" name="HJ_groupBox_GJ">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>460</y>
         <width>451</width>
         <height>211</height>
        </rect>
       </property>
       <property name="title">
        <string>高级设置</string>
       </property>
       <widget class="QLabel" name="GJ_label_PDmin">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>30</y>
          <width>101</width>
          <height>21</height>
         </rect>
        </property>
        <property name="text">
         <string>片段最短时长(s)</string>
        </property>
       </widget>
       <widget class="QSpinBox" name="GJ_spinBox_PDmin">
        <property name="geometry">
         <rect>
          <x>130</x>
          <y>30</y>
          <width>61</width>
          <height>23</height>
         </rect>
        </property>
        <property name="minimum">
         <number>1</number>
        </property>
        <property name="maximum">
         <number>15</number>
        </property>
        <property name="value">
         <number>3</number>
        </property>
       </widget>
       <widget class="QSpinBox" name="GJ_spinBox_PDmax">
        <property name="geometry">
         <rect>
          <x>340</x>
          <y>30</y>
          <width>61</width>
          <height>23</height>
         </rect>
        </property>
        <property name="minimum">
         <number>1</number>
        </property>
        <property name="maximum">
         <number>15</number>
        </property>
        <property name="value">
         <number>5</number>
        </property>
       </widget>
       <widget class="QLabel" name="GJ_label_PDmax">
        <property name="geometry">
         <rect>
          <x>230</x>
          <y>30</y>
          <width>101</width>
          <height>21</height>
         </rect>
        </property>
        <property name="text">
         <string>片段最长时长(s)</string>
        </property>
       </widget>
       <widget class="QSpinBox" name="GJ_spinBox_ZSCmin">
        <property name="geometry">
         <rect>
          <x>130</x>
          <y>80</y>
          <width>61</width>
          <height>23</height>
         </rect>
        </property>
        <property name="minimum">
         <number>1</number>
        </property>
        <property name="maximum">
         <number>50</number>
        </property>
        <property name="value">
         <number>15</number>
        </property>
       </widget>
       <widget class="QLabel" name="GJ_label_ZSCmin">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>80</y>
          <width>101</width>
          <height>21</height>
         </rect>
        </property>
        <property name="text">
         <string>总时长最短(s)</string>
        </property>
       </widget>
       <widget class="QLabel" name="GJ_label_ZSCmax">
        <property name="geometry">
         <rect>
          <x>230</x>
          <y>80</y>
          <width>101</width>
          <height>21</height>
         </rect>
        </property>
        <property name="text">
         <string>总时长最长(s)</string>
        </property>
       </widget>
       <widget class="QSpinBox" name="GJ_spinBox_ZSCmax">
        <property name="geometry">
         <rect>
          <x>340</x>
          <y>80</y>
          <width>61</width>
          <height>23</height>
         </rect>
        </property>
        <property name="minimum">
         <number>2</number>
        </property>
        <property name="maximum">
         <number>60</number>
        </property>
        <property name="value">
         <number>31</number>
        </property>
       </widget>
       <widget class="QCheckBox" name="GJ_checkBox">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>130</y>
          <width>201</width>
          <height>31</height>
         </rect>
        </property>
        <property name="text">
         <string>勾选时 可重复使用单条素材</string>
        </property>
       </widget>
       <widget class="QCheckBox" name="GJ_checkBox_2">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>170</y>
          <width>201</width>
          <height>31</height>
         </rect>
        </property>
        <property name="text">
         <string>勾选时 素材所有声音静音</string>
        </property>
       </widget>
      </widget>
     </widget>
     <widget class="QGroupBox" name="HJHX_groupBox_2">
      <property name="geometry">
       <rect>
        <x>740</x>
        <y>350</y>
        <width>421</width>
        <height>411</height>
       </rect>
      </property>
      <property name="title">
       <string>核心处理</string>
      </property>
      <widget class="QLabel" name="HJCL_label_3">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>30</y>
         <width>81</width>
         <height>16</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>false</bold>
        </font>
       </property>
       <property name="text">
        <string>处理日志</string>
       </property>
      </widget>
      <widget class="QPlainTextEdit" name="CL_plainTextEdit_2">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>50</y>
         <width>381</width>
         <height>241</height>
        </rect>
       </property>
      </widget>
      <widget class="QPushButton" name="HJKS_pushButton_2">
       <property name="geometry">
        <rect>
         <x>80</x>
         <y>350</y>
         <width>161</width>
         <height>41</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>0</red>
             <green>255</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>0</red>
             <green>255</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>0</red>
             <green>255</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>下面是见证奇迹的时刻</string>
       </property>
      </widget>
      <widget class="QPushButton" name="HJTZ_pushButton_2">
       <property name="geometry">
        <rect>
         <x>260</x>
         <y>350</y>
         <width>131</width>
         <height>41</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>0</green>
             <blue>4</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>0</green>
             <blue>4</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>0</green>
             <blue>4</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>停下来 停下来</string>
       </property>
      </widget>
      <widget class="QProgressBar" name="HJ_progressBar_2">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>310</y>
         <width>381</width>
         <height>23</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <bold>true</bold>
        </font>
       </property>
       <property name="value">
        <number>0</number>
       </property>
      </widget>
     </widget>
     <widget class="QGroupBox" name="HJ_groupBox_HJZC">
      <property name="geometry">
       <rect>
        <x>740</x>
        <y>20</y>
        <width>421</width>
        <height>131</height>
       </rect>
      </property>
      <property name="title">
       <string>转场设定</string>
      </property>
      <widget class="QCheckBox" name="HJ_checkBox_ZC">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>30</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>加转场</string>
       </property>
      </widget>
      <widget class="QComboBox" name="HJ_comboBox_ZC">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>70</y>
         <width>231</width>
         <height>41</height>
        </rect>
       </property>
       <item>
        <property name="text">
         <string>【【全随机】】</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【【柔 · 随机】】</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【【硬 · 随机】】</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【叠化】fade</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向左擦除】wipeleft</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向右擦除】wiperight</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向上擦除】wipeup</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向下擦除】wipedown</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【向左擦除】smoothleft</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【向右擦除】smoothright</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【向上擦除】smoothup</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【向下擦除】smoothdown</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向左滑动】slideleft</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向右滑动】slideright</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向上滑动】slideup</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向下滑动】slidedown</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【圆形展开】circleopen</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【圆形闭合】circleclose</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【垂直展开】vertopen</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【垂直闭合】vertclose</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【水平展开】horzopen</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【水平闭合】horzclose</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【景深转场】distance</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【时钟擦除】radial</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【像素模糊】pixelize</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【放大转场】zoomin</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【向左上擦除】diagtl</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【向右上擦除】diagtr</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【向左下擦除】diagbl</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【柔】【向右下擦除】diagbr</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向左上擦除】wipetl</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向右上擦除】wipetr</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向左下擦除】wipebl</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向右下擦除】wipebr</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向左百叶窗】hlslice</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向右百叶窗】hrslice</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向上百叶窗】vuslice</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向下百叶窗】vdslice</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向左滑刺】hlwind</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向右滑刺】hrwind</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向上滑刺】vuwind</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>【向下滑刺】vdwind</string>
        </property>
       </item>
      </widget>
      <widget class="QRadioButton" name="HJ_radioButton_y">
       <property name="geometry">
        <rect>
         <x>280</x>
         <y>40</y>
         <width>98</width>
         <height>19</height>
        </rect>
       </property>
       <property name="text">
        <string>每段都随机</string>
       </property>
      </widget>
      <widget class="QRadioButton" name="HJ_radioButton_n">
       <property name="geometry">
        <rect>
         <x>280</x>
         <y>80</y>
         <width>98</width>
         <height>19</height>
        </rect>
       </property>
       <property name="text">
        <string>只用一个转场</string>
       </property>
      </widget>
      <widget class="QDoubleSpinBox" name="ZC_doubleSpinBox_2">
       <property name="geometry">
        <rect>
         <x>190</x>
         <y>30</y>
         <width>71</width>
         <height>23</height>
        </rect>
       </property>
       <property name="decimals">
        <number>1</number>
       </property>
       <property name="minimum">
        <double>0.500000000000000</double>
       </property>
       <property name="maximum">
        <double>3.000000000000000</double>
       </property>
       <property name="value">
        <double>1.000000000000000</double>
       </property>
      </widget>
      <widget class="QLabel" name="ZC_label_11">
       <property name="geometry">
        <rect>
         <x>120</x>
         <y>30</y>
         <width>61</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>转场时长(s)</string>
       </property>
      </widget>
     </widget>
     <widget class="QGroupBox" name="KZ_groupBox_2">
      <property name="geometry">
       <rect>
        <x>510</x>
        <y>20</y>
        <width>221</width>
        <height>741</height>
       </rect>
      </property>
      <property name="title">
       <string>扩展功能</string>
      </property>
      <widget class="QCheckBox" name="CZ_checkBox_2">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>30</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>抽帧去重</string>
       </property>
      </widget>
      <widget class="QLabel" name="CZ_label_3">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>70</y>
         <width>71</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>最小抽帧数：</string>
       </property>
      </widget>
      <widget class="QLabel" name="CZ_label_4">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>150</y>
         <width>71</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>最大抽帧数：</string>
       </property>
      </widget>
      <widget class="QSpinBox" name="CZ_spinBox_low_2">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>90</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="minimum">
        <number>1</number>
       </property>
       <property name="value">
        <number>15</number>
       </property>
      </widget>
      <widget class="QSpinBox" name="CZ_spinBox_max_2">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>180</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="minimum">
        <number>2</number>
       </property>
       <property name="value">
        <number>50</number>
       </property>
      </widget>
      <widget class="QCheckBox" name="CMM_checkBox_6">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>500</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>批量重命名</string>
       </property>
      </widget>
      <widget class="QLabel" name="CMM_label_7">
       <property name="geometry">
        <rect>
         <x>30</x>
         <y>540</y>
         <width>71</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>起始值：</string>
       </property>
      </widget>
      <widget class="QSpinBox" name="CMM_spinBox_3">
       <property name="geometry">
        <rect>
         <x>100</x>
         <y>530</y>
         <width>71</width>
         <height>31</height>
        </rect>
       </property>
       <property name="minimum">
        <number>1</number>
       </property>
       <property name="maximum">
        <number>99999</number>
       </property>
      </widget>
      <widget class="QLabel" name="CMM_label_8">
       <property name="geometry">
        <rect>
         <x>30</x>
         <y>570</y>
         <width>71</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>名称前缀：</string>
       </property>
      </widget>
      <widget class="QLineEdit" name="CMM_lineEdit_5">
       <property name="geometry">
        <rect>
         <x>30</x>
         <y>590</y>
         <width>121</width>
         <height>31</height>
        </rect>
       </property>
      </widget>
      <widget class="QLabel" name="CMM_label_9">
       <property name="geometry">
        <rect>
         <x>30</x>
         <y>630</y>
         <width>71</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>名称后缀：</string>
       </property>
      </widget>
      <widget class="QLineEdit" name="CMM_lineEdit_6">
       <property name="geometry">
        <rect>
         <x>30</x>
         <y>650</y>
         <width>121</width>
         <height>31</height>
        </rect>
       </property>
      </widget>
      <widget class="QCheckBox" name="CMM_checkBox_7">
       <property name="geometry">
        <rect>
         <x>30</x>
         <y>700</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="text">
        <string>填充0来补足长度</string>
       </property>
       <property name="checked">
        <bool>true</bool>
       </property>
       <property name="tristate">
        <bool>false</bool>
       </property>
      </widget>
      <widget class="QSpinBox" name="spinBox_3">
       <property name="geometry">
        <rect>
         <x>140</x>
         <y>700</y>
         <width>61</width>
         <height>23</height>
        </rect>
       </property>
       <property name="value">
        <number>2</number>
       </property>
      </widget>
      <widget class="Line" name="line_7">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>220</y>
         <width>201</width>
         <height>20</height>
        </rect>
       </property>
       <property name="lineWidth">
        <number>2</number>
       </property>
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
      </widget>
      <widget class="QCheckBox" name="YY_checkBox_3">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>240</y>
         <width>151</width>
         <height>31</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>整体添加背景音乐</string>
       </property>
      </widget>
      <widget class="QPushButton" name="YY_pushButton_2">
       <property name="geometry">
        <rect>
         <x>60</x>
         <y>320</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>170</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>170</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>170</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="text">
        <string>选择音乐目录</string>
       </property>
      </widget>
      <widget class="QLineEdit" name="YY_lineEdit_2">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>280</y>
         <width>191</width>
         <height>31</height>
        </rect>
       </property>
      </widget>
      <widget class="QLabel" name="YY_label_6">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>360</y>
         <width>71</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>原视频音量：</string>
       </property>
      </widget>
      <widget class="QLabel" name="YY_label_7">
       <property name="geometry">
        <rect>
         <x>120</x>
         <y>360</y>
         <width>71</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>音乐音量：</string>
       </property>
      </widget>
      <widget class="QSpinBox" name="YY_spinBox_yspyl_2">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>390</y>
         <width>71</width>
         <height>31</height>
        </rect>
       </property>
       <property name="minimum">
        <number>-30</number>
       </property>
       <property name="maximum">
        <number>15</number>
       </property>
       <property name="value">
        <number>-5</number>
       </property>
      </widget>
      <widget class="QSpinBox" name="YY_spinBox_yyyl_2">
       <property name="geometry">
        <rect>
         <x>120</x>
         <y>390</y>
         <width>71</width>
         <height>31</height>
        </rect>
       </property>
       <property name="minimum">
        <number>-30</number>
       </property>
       <property name="maximum">
        <number>15</number>
       </property>
       <property name="value">
        <number>-10</number>
       </property>
      </widget>
      <widget class="QLabel" name="YY_label_8">
       <property name="geometry">
        <rect>
         <x>90</x>
         <y>400</y>
         <width>21</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>dB</string>
       </property>
      </widget>
      <widget class="QLabel" name="YY_label_9">
       <property name="geometry">
        <rect>
         <x>200</x>
         <y>400</y>
         <width>21</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>dB</string>
       </property>
      </widget>
      <widget class="QLabel" name="YY_label_10">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>430</y>
         <width>201</width>
         <height>31</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="WindowText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>254</red>
             <green>0</green>
             <blue>4</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Text">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="ToolTipText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="WindowText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>254</red>
             <green>0</green>
             <blue>4</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Text">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="ToolTipText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="ToolTipText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="contextMenuPolicy">
        <enum>Qt::ContextMenuPolicy::ActionsContextMenu</enum>
       </property>
       <property name="text">
        <string>注：音量范围为-30dB到15dB</string>
       </property>
       <property name="wordWrap">
        <bool>true</bool>
       </property>
      </widget>
      <widget class="Line" name="line_8">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>470</y>
         <width>201</width>
         <height>20</height>
        </rect>
       </property>
       <property name="lineWidth">
        <number>2</number>
       </property>
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
      </widget>
     </widget>
     <widget class="QGroupBox" name="HJ_groupBox_LK">
      <property name="geometry">
       <rect>
        <x>740</x>
        <y>160</y>
        <width>421</width>
        <height>181</height>
       </rect>
      </property>
      <property name="title">
       <string>落款设定</string>
      </property>
      <widget class="QCheckBox" name="HJ_checkBox_LK">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>30</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>加落款</string>
       </property>
      </widget>
      <widget class="QLineEdit" name="LK_lineEdit_3">
       <property name="geometry">
        <rect>
         <x>130</x>
         <y>60</y>
         <width>161</width>
         <height>31</height>
        </rect>
       </property>
      </widget>
      <widget class="QPushButton" name="LK_pushButton_3">
       <property name="geometry">
        <rect>
         <x>300</x>
         <y>60</y>
         <width>101</width>
         <height>31</height>
        </rect>
       </property>
       <property name="text">
        <string>选择落款目录</string>
       </property>
      </widget>
      <widget class="QRadioButton" name="LK_radioButton_duo">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>70</y>
         <width>91</width>
         <height>19</height>
        </rect>
       </property>
       <property name="text">
        <string>多个随机</string>
       </property>
      </widget>
      <widget class="QRadioButton" name="LK_radioButton_dan">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>130</y>
         <width>91</width>
         <height>19</height>
        </rect>
       </property>
       <property name="text">
        <string>单个</string>
       </property>
      </widget>
      <widget class="QListView" name="LK_listView">
       <property name="geometry">
        <rect>
         <x>90</x>
         <y>110</y>
         <width>191</width>
         <height>51</height>
        </rect>
       </property>
      </widget>
      <widget class="QPushButton" name="LK_in_5">
       <property name="geometry">
        <rect>
         <x>300</x>
         <y>120</y>
         <width>51</width>
         <height>41</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="text">
        <string>添加</string>
       </property>
      </widget>
      <widget class="QPushButton" name="LK_clean_5">
       <property name="geometry">
        <rect>
         <x>360</x>
         <y>120</y>
         <width>51</width>
         <height>41</height>
        </rect>
       </property>
       <property name="text">
        <string>清空</string>
       </property>
      </widget>
      <widget class="QLabel" name="LK_label">
       <property name="geometry">
        <rect>
         <x>160</x>
         <y>20</y>
         <width>171</width>
         <height>21</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="WindowText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>255</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Text">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="WindowText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>255</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Text">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled/>
        </palette>
       </property>
       <property name="text">
        <string>注：落款不计入混剪总时长</string>
       </property>
      </widget>
     </widget>
    </widget>
    <widget class="QWidget" name="XSC_tab">
     <attribute name="title">
      <string>洗素材</string>
     </attribute>
     <widget class="QGroupBox" name="XSC_groupBox_2">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>20</y>
        <width>551</width>
        <height>751</height>
       </rect>
      </property>
      <property name="title">
       <string>素材设定区</string>
      </property>
      <widget class="QLabel" name="XSC_name_4">
       <property name="geometry">
        <rect>
         <x>50</x>
         <y>40</y>
         <width>91</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string>要洗的文件</string>
       </property>
      </widget>
      <widget class="QListView" name="XSC_listView_4">
       <property name="geometry">
        <rect>
         <x>40</x>
         <y>60</y>
         <width>361</width>
         <height>271</height>
        </rect>
       </property>
      </widget>
      <widget class="QPushButton" name="XSC_in_4">
       <property name="geometry">
        <rect>
         <x>50</x>
         <y>350</y>
         <width>111</width>
         <height>41</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="text">
        <string>添加文件</string>
       </property>
      </widget>
      <widget class="QPushButton" name="XSC_clean_4">
       <property name="geometry">
        <rect>
         <x>270</x>
         <y>350</y>
         <width>111</width>
         <height>41</height>
        </rect>
       </property>
       <property name="text">
        <string>清空列表</string>
       </property>
      </widget>
      <widget class="QPushButton" name="XSC_out_4">
       <property name="geometry">
        <rect>
         <x>160</x>
         <y>350</y>
         <width>111</width>
         <height>41</height>
        </rect>
       </property>
       <property name="text">
        <string>移除选中</string>
       </property>
      </widget>
      <widget class="QRadioButton" name="XSC_radioButton_TH_2">
       <property name="geometry">
        <rect>
         <x>60</x>
         <y>450</y>
         <width>98</width>
         <height>19</height>
        </rect>
       </property>
       <property name="text">
        <string>替换原素材</string>
       </property>
       <property name="checked">
        <bool>true</bool>
       </property>
      </widget>
      <widget class="QRadioButton" name="XSC_radioButton_ZD_2">
       <property name="geometry">
        <rect>
         <x>220</x>
         <y>450</y>
         <width>98</width>
         <height>19</height>
        </rect>
       </property>
       <property name="text">
        <string>指定输出目录</string>
       </property>
       <property name="checked">
        <bool>false</bool>
       </property>
      </widget>
      <widget class="QWidget" name="XSC_widget_SC_2" native="true">
       <property name="geometry">
        <rect>
         <x>40</x>
         <y>500</y>
         <width>501</width>
         <height>81</height>
        </rect>
       </property>
       <widget class="QPushButton" name="XSC_where_4">
        <property name="geometry">
         <rect>
          <x>380</x>
          <y>30</y>
          <width>111</width>
          <height>31</height>
         </rect>
        </property>
        <property name="palette">
         <palette>
          <active>
           <colorrole role="Button">
            <brush brushstyle="SolidPattern">
             <color alpha="179">
              <red>255</red>
              <green>235</green>
              <blue>15</blue>
             </color>
            </brush>
           </colorrole>
           <colorrole role="Light">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>255</green>
              <blue>127</blue>
             </color>
            </brush>
           </colorrole>
           <colorrole role="Midlight">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>255</green>
              <blue>127</blue>
             </color>
            </brush>
           </colorrole>
          </active>
          <inactive>
           <colorrole role="Button">
            <brush brushstyle="SolidPattern">
             <color alpha="179">
              <red>255</red>
              <green>235</green>
              <blue>15</blue>
             </color>
            </brush>
           </colorrole>
           <colorrole role="Light">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>255</green>
              <blue>127</blue>
             </color>
            </brush>
           </colorrole>
           <colorrole role="Midlight">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>255</green>
              <blue>127</blue>
             </color>
            </brush>
           </colorrole>
          </inactive>
          <disabled>
           <colorrole role="Button">
            <brush brushstyle="SolidPattern">
             <color alpha="179">
              <red>255</red>
              <green>235</green>
              <blue>15</blue>
             </color>
            </brush>
           </colorrole>
           <colorrole role="Light">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>255</green>
              <blue>127</blue>
             </color>
            </brush>
           </colorrole>
           <colorrole role="Midlight">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>255</green>
              <blue>127</blue>
             </color>
            </brush>
           </colorrole>
          </disabled>
         </palette>
        </property>
        <property name="text">
         <string>选择输出目录</string>
        </property>
       </widget>
       <widget class="QLabel" name="XSC_outname_4">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>10</y>
          <width>91</width>
          <height>16</height>
         </rect>
        </property>
        <property name="text">
         <string>输出目录：</string>
        </property>
       </widget>
       <widget class="QLineEdit" name="XSC_lineEdit_4">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>30</y>
          <width>321</width>
          <height>31</height>
         </rect>
        </property>
       </widget>
      </widget>
      <widget class="QLabel" name="XSC_label">
       <property name="geometry">
        <rect>
         <x>60</x>
         <y>610</y>
         <width>451</width>
         <height>111</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
        </font>
       </property>
       <property name="text">
        <string>【替换原素材】时无法选择输出目录，直接在源文件夹里覆盖掉原素材了</string>
       </property>
      </widget>
     </widget>
     <widget class="QGroupBox" name="XSC_groupBox_FBL">
      <property name="geometry">
       <rect>
        <x>580</x>
        <y>20</y>
        <width>561</width>
        <height>91</height>
       </rect>
      </property>
      <property name="title">
       <string>目标分辨率</string>
      </property>
      <widget class="QLabel" name="W_label">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>40</y>
         <width>91</width>
         <height>31</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="text">
        <string>宽度：</string>
       </property>
      </widget>
      <widget class="QTextEdit" name="SXC_textEdit_W">
       <property name="geometry">
        <rect>
         <x>70</x>
         <y>40</y>
         <width>111</width>
         <height>31</height>
        </rect>
       </property>
       <property name="html">
        <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;meta charset=&quot;utf-8&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
hr { height: 1px; border-width: 0; }
li.unchecked::marker { content: &quot;\2610&quot;; }
li.checked::marker { content: &quot;\2612&quot;; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'Microsoft YaHei UI'; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;1080&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
      </widget>
      <widget class="QLabel" name="H_label">
       <property name="geometry">
        <rect>
         <x>220</x>
         <y>40</y>
         <width>91</width>
         <height>31</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="text">
        <string>高度：</string>
       </property>
      </widget>
      <widget class="QTextEdit" name="SXC_textEdit_H">
       <property name="geometry">
        <rect>
         <x>270</x>
         <y>40</y>
         <width>111</width>
         <height>31</height>
        </rect>
       </property>
       <property name="html">
        <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;meta charset=&quot;utf-8&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
hr { height: 1px; border-width: 0; }
li.unchecked::marker { content: &quot;\2610&quot;; }
li.checked::marker { content: &quot;\2612&quot;; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'Microsoft YaHei UI'; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;1920&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
      </widget>
      <widget class="QLabel" name="label">
       <property name="geometry">
        <rect>
         <x>440</x>
         <y>40</y>
         <width>71</width>
         <height>31</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>固定30帧</string>
       </property>
      </widget>
     </widget>
     <widget class="QGroupBox" name="XSC_groupBox_BTL">
      <property name="geometry">
       <rect>
        <x>580</x>
        <y>130</y>
        <width>561</width>
        <height>91</height>
       </rect>
      </property>
      <property name="title">
       <string>目标视频比特率（kbps）</string>
      </property>
      <widget class="QLabel" name="BTL_label">
       <property name="geometry">
        <rect>
         <x>180</x>
         <y>30</y>
         <width>361</width>
         <height>41</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="text">
        <string>建议值：原视频比特率左右即可，无脑12000也行</string>
       </property>
      </widget>
      <widget class="QTextEdit" name="SXC_textEdit_BTL">
       <property name="geometry">
        <rect>
         <x>30</x>
         <y>40</y>
         <width>121</width>
         <height>31</height>
        </rect>
       </property>
       <property name="html">
        <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;meta charset=&quot;utf-8&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
hr { height: 1px; border-width: 0; }
li.unchecked::marker { content: &quot;\2610&quot;; }
li.checked::marker { content: &quot;\2612&quot;; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'Microsoft YaHei UI'; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;12000&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
      </widget>
     </widget>
     <widget class="QGroupBox" name="XSC_groupBox_3KS">
      <property name="geometry">
       <rect>
        <x>580</x>
        <y>240</y>
        <width>561</width>
        <height>531</height>
       </rect>
      </property>
      <property name="title">
       <string>核心处理</string>
      </property>
      <widget class="QLabel" name="XSC_label_4RZ">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>30</y>
         <width>81</width>
         <height>16</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>false</bold>
        </font>
       </property>
       <property name="text">
        <string>处理日志</string>
       </property>
      </widget>
      <widget class="QPlainTextEdit" name="XSC_plainTextEdit_3RZ">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>50</y>
         <width>511</width>
         <height>351</height>
        </rect>
       </property>
      </widget>
      <widget class="QPushButton" name="XSC_pushButton_3">
       <property name="geometry">
        <rect>
         <x>110</x>
         <y>470</y>
         <width>201</width>
         <height>41</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>0</red>
             <green>255</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>0</red>
             <green>255</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>0</red>
             <green>255</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>废话少说</string>
       </property>
      </widget>
      <widget class="QPushButton" name="XSC_pushButton_4">
       <property name="geometry">
        <rect>
         <x>330</x>
         <y>470</y>
         <width>191</width>
         <height>41</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>0</green>
             <blue>4</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>0</green>
             <blue>4</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>0</green>
             <blue>4</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>可能停不下来的停止按钮</string>
       </property>
      </widget>
      <widget class="QProgressBar" name="XSC_progressBar_3">
       <property name="geometry">
        <rect>
         <x>150</x>
         <y>430</y>
         <width>381</width>
         <height>23</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <bold>true</bold>
        </font>
       </property>
       <property name="value">
        <number>0</number>
       </property>
      </widget>
     </widget>
    </widget>
    <widget class="QWidget" name="tab_SY">
     <attribute name="title">
      <string>水印工具</string>
     </attribute>
     <widget class="QGroupBox" name="SY_groupBox_3">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>20</y>
        <width>551</width>
        <height>751</height>
       </rect>
      </property>
      <property name="title">
       <string>素材设定区</string>
      </property>
      <widget class="QLabel" name="SY_name_5">
       <property name="geometry">
        <rect>
         <x>50</x>
         <y>40</y>
         <width>201</width>
         <height>16</height>
        </rect>
       </property>
       <property name="text">
        <string>需要添加水印的视频</string>
       </property>
      </widget>
      <widget class="QListView" name="SY_listView_5">
       <property name="geometry">
        <rect>
         <x>40</x>
         <y>60</y>
         <width>361</width>
         <height>271</height>
        </rect>
       </property>
      </widget>
      <widget class="QPushButton" name="SY_in_5">
       <property name="geometry">
        <rect>
         <x>50</x>
         <y>350</y>
         <width>111</width>
         <height>41</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>221</red>
             <green>255</green>
             <blue>254</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="text">
        <string>添加文件</string>
       </property>
      </widget>
      <widget class="QPushButton" name="SY_clean_5">
       <property name="geometry">
        <rect>
         <x>270</x>
         <y>350</y>
         <width>111</width>
         <height>41</height>
        </rect>
       </property>
       <property name="text">
        <string>清空列表</string>
       </property>
      </widget>
      <widget class="QPushButton" name="SY_out_5">
       <property name="geometry">
        <rect>
         <x>160</x>
         <y>350</y>
         <width>111</width>
         <height>41</height>
        </rect>
       </property>
       <property name="text">
        <string>移除选中</string>
       </property>
      </widget>
      <widget class="QRadioButton" name="SY_radioButton_TH_3">
       <property name="geometry">
        <rect>
         <x>60</x>
         <y>450</y>
         <width>98</width>
         <height>19</height>
        </rect>
       </property>
       <property name="text">
        <string>替换原素材</string>
       </property>
       <property name="checked">
        <bool>true</bool>
       </property>
      </widget>
      <widget class="QRadioButton" name="SY_radioButton_ZD_3">
       <property name="geometry">
        <rect>
         <x>220</x>
         <y>450</y>
         <width>98</width>
         <height>19</height>
        </rect>
       </property>
       <property name="text">
        <string>指定输出目录</string>
       </property>
       <property name="checked">
        <bool>false</bool>
       </property>
      </widget>
      <widget class="QWidget" name="SY_widget_SC_3" native="true">
       <property name="geometry">
        <rect>
         <x>40</x>
         <y>500</y>
         <width>501</width>
         <height>81</height>
        </rect>
       </property>
       <widget class="QPushButton" name="SY_where_5">
        <property name="geometry">
         <rect>
          <x>380</x>
          <y>30</y>
          <width>111</width>
          <height>31</height>
         </rect>
        </property>
        <property name="palette">
         <palette>
          <active>
           <colorrole role="Button">
            <brush brushstyle="SolidPattern">
             <color alpha="179">
              <red>255</red>
              <green>235</green>
              <blue>15</blue>
             </color>
            </brush>
           </colorrole>
           <colorrole role="Light">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>255</green>
              <blue>127</blue>
             </color>
            </brush>
           </colorrole>
           <colorrole role="Midlight">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>255</green>
              <blue>127</blue>
             </color>
            </brush>
           </colorrole>
          </active>
          <inactive>
           <colorrole role="Button">
            <brush brushstyle="SolidPattern">
             <color alpha="179">
              <red>255</red>
              <green>235</green>
              <blue>15</blue>
             </color>
            </brush>
           </colorrole>
           <colorrole role="Light">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>255</green>
              <blue>127</blue>
             </color>
            </brush>
           </colorrole>
           <colorrole role="Midlight">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>255</green>
              <blue>127</blue>
             </color>
            </brush>
           </colorrole>
          </inactive>
          <disabled>
           <colorrole role="Button">
            <brush brushstyle="SolidPattern">
             <color alpha="179">
              <red>255</red>
              <green>235</green>
              <blue>15</blue>
             </color>
            </brush>
           </colorrole>
           <colorrole role="Light">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>255</green>
              <blue>127</blue>
             </color>
            </brush>
           </colorrole>
           <colorrole role="Midlight">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>255</green>
              <blue>127</blue>
             </color>
            </brush>
           </colorrole>
          </disabled>
         </palette>
        </property>
        <property name="text">
         <string>选择输出目录</string>
        </property>
       </widget>
       <widget class="QLabel" name="SY_outname_5">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>10</y>
          <width>91</width>
          <height>16</height>
         </rect>
        </property>
        <property name="text">
         <string>输出目录：</string>
        </property>
       </widget>
       <widget class="QLineEdit" name="SY_lineEdit_5">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>30</y>
          <width>321</width>
          <height>31</height>
         </rect>
        </property>
       </widget>
      </widget>
      <widget class="QLabel" name="SY_label_2">
       <property name="geometry">
        <rect>
         <x>60</x>
         <y>610</y>
         <width>451</width>
         <height>111</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
        </font>
       </property>
       <property name="text">
        <string>【替换原素材】时无法选择输出目录，直接在源文件夹里覆盖掉原素材了</string>
       </property>
      </widget>
      <widget class="QLabel" name="SY_label_12">
       <property name="geometry">
        <rect>
         <x>60</x>
         <y>400</y>
         <width>201</width>
         <height>31</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="WindowText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>254</red>
             <green>0</green>
             <blue>4</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Text">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="ToolTipText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="WindowText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>254</red>
             <green>0</green>
             <blue>4</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Text">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="ToolTipText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="ToolTipText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="contextMenuPolicy">
        <enum>Qt::ContextMenuPolicy::ActionsContextMenu</enum>
       </property>
       <property name="text">
        <string>注：视频用1080P的</string>
       </property>
       <property name="wordWrap">
        <bool>true</bool>
       </property>
      </widget>
     </widget>
     <widget class="QGroupBox" name="SY_groupBox_3KS_2">
      <property name="geometry">
       <rect>
        <x>580</x>
        <y>290</y>
        <width>561</width>
        <height>481</height>
       </rect>
      </property>
      <property name="title">
       <string>核心处理</string>
      </property>
      <widget class="QLabel" name="SY_label_4RZ_2">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>40</y>
         <width>81</width>
         <height>16</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>false</bold>
        </font>
       </property>
       <property name="text">
        <string>处理日志</string>
       </property>
      </widget>
      <widget class="QPlainTextEdit" name="SY_plainTextEdit_3RZ_2">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>60</y>
         <width>511</width>
         <height>301</height>
        </rect>
       </property>
      </widget>
      <widget class="QPushButton" name="SY_pushButton_5">
       <property name="geometry">
        <rect>
         <x>110</x>
         <y>420</y>
         <width>201</width>
         <height>41</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>0</red>
             <green>255</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>0</red>
             <green>255</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>0</red>
             <green>255</green>
             <blue>255</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>顷刻炼化</string>
       </property>
      </widget>
      <widget class="QPushButton" name="SY_pushButton_6">
       <property name="geometry">
        <rect>
         <x>330</x>
         <y>420</y>
         <width>191</width>
         <height>41</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>0</green>
             <blue>4</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>0</green>
             <blue>4</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>0</green>
             <blue>4</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>哟，你先等等</string>
       </property>
      </widget>
      <widget class="QProgressBar" name="SY_progressBar_4">
       <property name="geometry">
        <rect>
         <x>150</x>
         <y>380</y>
         <width>381</width>
         <height>23</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <bold>true</bold>
        </font>
       </property>
       <property name="value">
        <number>0</number>
       </property>
      </widget>
     </widget>
     <widget class="QGroupBox" name="SY_groupBox_sy">
      <property name="geometry">
       <rect>
        <x>580</x>
        <y>20</y>
        <width>561</width>
        <height>251</height>
       </rect>
      </property>
      <property name="title">
       <string>水印指定区</string>
      </property>
      <widget class="Line" name="line_5">
       <property name="geometry">
        <rect>
         <x>260</x>
         <y>30</y>
         <width>20</width>
         <height>201</height>
        </rect>
       </property>
       <property name="orientation">
        <enum>Qt::Orientation::Vertical</enum>
       </property>
      </widget>
      <widget class="QRadioButton" name="SY_radioButton_ZD">
       <property name="geometry">
        <rect>
         <x>300</x>
         <y>30</y>
         <width>221</width>
         <height>21</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>false</bold>
        </font>
       </property>
       <property name="text">
        <string>自定义 （仅全屏图片）</string>
       </property>
      </widget>
      <widget class="QRadioButton" name="SY_radioButton_HHLOGO">
       <property name="geometry">
        <rect>
         <x>50</x>
         <y>70</y>
         <width>111</width>
         <height>19</height>
        </rect>
       </property>
       <property name="text">
        <string>全屏合合LOGO</string>
       </property>
      </widget>
      <widget class="QRadioButton" name="SY_radioButton_HHTPSJ">
       <property name="geometry">
        <rect>
         <x>50</x>
         <y>110</y>
         <width>101</width>
         <height>19</height>
        </rect>
       </property>
       <property name="text">
        <string>预设全屏图片</string>
       </property>
      </widget>
      <widget class="QRadioButton" name="SY_radioButton_HHlogoJB">
       <property name="geometry">
        <rect>
         <x>50</x>
         <y>150</y>
         <width>161</width>
         <height>19</height>
        </rect>
       </property>
       <property name="text">
        <string>合合LOGO角标  (100%)</string>
       </property>
      </widget>
      <widget class="QRadioButton" name="radioButton_LUP">
       <property name="geometry">
        <rect>
         <x>100</x>
         <y>180</y>
         <width>51</width>
         <height>19</height>
        </rect>
       </property>
       <property name="text">
        <string>左上</string>
       </property>
      </widget>
      <widget class="QRadioButton" name="radioButton_RUP">
       <property name="geometry">
        <rect>
         <x>160</x>
         <y>180</y>
         <width>51</width>
         <height>19</height>
        </rect>
       </property>
       <property name="text">
        <string>右上</string>
       </property>
      </widget>
      <widget class="QRadioButton" name="radioButton_RDW">
       <property name="geometry">
        <rect>
         <x>160</x>
         <y>210</y>
         <width>51</width>
         <height>19</height>
        </rect>
       </property>
       <property name="text">
        <string>右下</string>
       </property>
      </widget>
      <widget class="QRadioButton" name="radioButton_LDW">
       <property name="geometry">
        <rect>
         <x>100</x>
         <y>210</y>
         <width>51</width>
         <height>19</height>
        </rect>
       </property>
       <property name="text">
        <string>左下</string>
       </property>
      </widget>
      <widget class="QPushButton" name="SY_where_6ZD">
       <property name="geometry">
        <rect>
         <x>460</x>
         <y>80</y>
         <width>81</width>
         <height>31</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>235</green>
             <blue>15</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Light">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Midlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>235</green>
             <blue>15</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Light">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Midlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Button">
           <brush brushstyle="SolidPattern">
            <color alpha="179">
             <red>255</red>
             <green>235</green>
             <blue>15</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Light">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Midlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>255</red>
             <green>255</green>
             <blue>127</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="text">
        <string>选择目录</string>
       </property>
      </widget>
      <widget class="QLineEdit" name="SY_lineEdit_6ZD">
       <property name="geometry">
        <rect>
         <x>290</x>
         <y>80</y>
         <width>151</width>
         <height>31</height>
        </rect>
       </property>
      </widget>
      <widget class="QSpinBox" name="SY_spinBox_ZDSZ">
       <property name="geometry">
        <rect>
         <x>420</x>
         <y>150</y>
         <width>91</width>
         <height>31</height>
        </rect>
       </property>
       <property name="minimum">
        <number>0</number>
       </property>
       <property name="maximum">
        <number>100</number>
       </property>
       <property name="value">
        <number>10</number>
       </property>
      </widget>
      <widget class="QLabel" name="SY_label_ZDZD">
       <property name="geometry">
        <rect>
         <x>300</x>
         <y>160</y>
         <width>101</width>
         <height>21</height>
        </rect>
       </property>
       <property name="text">
        <string>不透明度（%）</string>
       </property>
      </widget>
      <widget class="QLabel" name="SY_label_11">
       <property name="geometry">
        <rect>
         <x>290</x>
         <y>200</y>
         <width>201</width>
         <height>31</height>
        </rect>
       </property>
       <property name="palette">
        <palette>
         <active>
          <colorrole role="WindowText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>254</red>
             <green>0</green>
             <blue>4</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Text">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="ToolTipText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="WindowText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>254</red>
             <green>0</green>
             <blue>4</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="Text">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
          <colorrole role="ToolTipText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="ToolTipText">
           <brush brushstyle="SolidPattern">
            <color alpha="228">
             <red>0</red>
             <green>0</green>
             <blue>0</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="contextMenuPolicy">
        <enum>Qt::ContextMenuPolicy::ActionsContextMenu</enum>
       </property>
       <property name="text">
        <string>注：图片尽量与视频尺寸一致</string>
       </property>
       <property name="wordWrap">
        <bool>true</bool>
       </property>
      </widget>
      <widget class="QRadioButton" name="SY_radioButton_HHLOGO_2">
       <property name="geometry">
        <rect>
         <x>50</x>
         <y>30</y>
         <width>111</width>
         <height>19</height>
        </rect>
       </property>
       <property name="text">
        <string>动态合合LOGO</string>
       </property>
      </widget>
      <widget class="QSpinBox" name="SY_spinBox_ZDSZ_2">
       <property name="geometry">
        <rect>
         <x>170</x>
         <y>20</y>
         <width>71</width>
         <height>31</height>
        </rect>
       </property>
       <property name="minimum">
        <number>0</number>
       </property>
       <property name="maximum">
        <number>100</number>
       </property>
       <property name="value">
        <number>15</number>
       </property>
      </widget>
      <widget class="QSpinBox" name="SY_spinBox_ZDSZ_3">
       <property name="geometry">
        <rect>
         <x>170</x>
         <y>60</y>
         <width>71</width>
         <height>31</height>
        </rect>
       </property>
       <property name="minimum">
        <number>0</number>
       </property>
       <property name="maximum">
        <number>100</number>
       </property>
       <property name="value">
        <number>6</number>
       </property>
      </widget>
      <widget class="QSpinBox" name="SY_spinBox_ZDSZ_4">
       <property name="geometry">
        <rect>
         <x>170</x>
         <y>100</y>
         <width>71</width>
         <height>31</height>
        </rect>
       </property>
       <property name="minimum">
        <number>0</number>
       </property>
       <property name="maximum">
        <number>100</number>
       </property>
       <property name="value">
        <number>8</number>
       </property>
      </widget>
      <widget class="QCheckBox" name="checkBox_AI">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>220</y>
         <width>71</width>
         <height>19</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <bold>true</bold>
        </font>
       </property>
       <property name="text">
        <string>AI标识</string>
       </property>
      </widget>
      <widget class="QRadioButton" name="SY_radioButton_nothing">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>190</y>
         <width>51</width>
         <height>19</height>
        </rect>
       </property>
       <property name="text">
        <string>无</string>
       </property>
      </widget>
     </widget>
    </widget>
   </widget>
   <widget class="QCheckBox" name="checkBox">
    <property name="geometry">
     <rect>
      <x>1080</x>
      <y>0</y>
      <width>83</width>
      <height>19</height>
     </rect>
    </property>
    <property name="text">
     <string>界面反色</string>
    </property>
   </widget>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1186</width>
     <height>33</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuMFChen_v0_1">
    <property name="title">
     <string>MFChen视频混剪工具 v2.7</string>
    </property>
   </widget>
   <addaction name="menuMFChen_v0_1"/>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
