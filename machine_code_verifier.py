import uuid
import hashlib
import json
import os
import platform
import subprocess
import sys
import time
from datetime import datetime, timedelta

# Windows下隐藏控制台窗口
if sys.platform.startswith('win'):
    CREATE_NO_WINDOW = 0x08000000
else:
    CREATE_NO_WINDOW = 0

# 尝试导入PySide6，如果失败则设置标志
try:
    from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox, QTextEdit
    from PySide6.QtCore import Qt
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    # 创建虚拟类以避免错误
    class QWidget: pass
    class QApplication: pass
    class Qt:
        AlignCenter = None

class MachineCodeVerifier:
    def __init__(self, app_name="YourApp"):
        self.app_name = app_name
        self.machine_id = self._get_machine_id()

        # 密钥盐值 - 用于机器码转MD5（与转换工具保持一致）
        self.salt_key = "MFChen_Video_Tool_2025_Secret_Key_MDZNB&*"

        # 预设的授权配置 - 包含MD5值和过期时间
        self.authorized_config = {
            "F168DDA7BD43EF480A7A2A2782F2D248": {
                "expire_date": "2030-06-06",  # 过期日期 YYYY-MM-DD
                "description": "当前测试机器"
            },
            "BA00AECDB6DF2324140344BD04908D62": {
                "expire_date": "2030-06-06",
                "description": "刘若愚的机器"
            },
            "B3C8A19C0C714D04E2CFBF74C5AAAB60": {
                "expire_date": "2030-06-06",
                "description": "刘若愚的机器2"
            },
            "4128B2049362134C1C8C35DE3E754198": {
                "expire_date": "2030-06-06",
                "description": "刘若愚的机器3"
            },
            "BE534607DC3F30F044AA69EF163B8B74": {
                "expire_date": "2030-06-06",
                "description": "刘若愚的机器4"
            },
            "B5EA4F10195B72FC0D8965B7A19508F1": {
                "expire_date": "2030-06-06",
                "description": "刘若愚的机器5"
            },
            "0559F3B5D362941932BEAD1078AA09AD": {
                "expire_date": "2030-06-06",
                "description": "刘若愚的机器6"
            },
            "F97879EE65BB5CB1D874171C4184D6D3": {
                "expire_date": "2030-06-06",
                "description": "刘若愚的机器7"
                
            },
            "16A0F9ED3C0D11CBA797294CE9C0E115": {
                "expire_date": "2030-06-06",
                "description": "刘若愚的机器8"
                
            },
            "991EF0C855F11CF6D02BEB686CC7AB12": {
                "expire_date": "2030-06-06",
                "description": "李家康的机器"
            },
            "2E0E078C1215BE2F84A421278839652D": {
                "expire_date": "2030-06-06",
                "description": "张鹏程（真白）的机器"
            },
            "BABA53F3F902AF372C837F934325C0C8": {
                "expire_date": "2030-06-06",
                "description": "张鹏程（真白）的机器2"
            },
            "C92B2D5DA2FE237D70AE510D6CE554CE": {
                "expire_date": "2030-06-06",
                "description": "张鹏程（真白）的机器3"
            },
            "CE19E6F573FD457B968F05B37B710E71": {
                "expire_date": "2030-06-06",
                "description": "罗慧（实实）的机器"
            },
            "A5F42F3D8AC92549DCAED0E42057420F": {
                "expire_date": "2030-06-06",
                "description": "罗慧（实实）的机器2"
            },
            "1B034810DD2DCEC460D7B6DE61744902": {
                "expire_date": "2030-06-06",
                "description": "李建萌的机器"
            },
            # 可以添加更多授权配置
        }

        # 延迟初始化时间验证器
        self.time_validator = None
        self.time_validation_enabled = None  # 延迟检查

    def _get_machine_id(self):
        """生成基于多种硬件信息的稳定唯一机器码"""
        machine_info = []

        try:
            # 获取主板序列号
            if os.name == "nt":  # Windows
                try:
                    result = subprocess.run(['wmic', 'baseboard', 'get', 'serialnumber'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines:
                            if line.strip() and 'SerialNumber' not in line:
                                machine_info.append(line.strip())
                except:
                    pass

                # 获取CPU序列号
                try:
                    result = subprocess.run(['wmic', 'cpu', 'get', 'processorid'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines:
                            if line.strip() and 'ProcessorId' not in line:
                                machine_info.append(line.strip())
                except:
                    pass

                # 获取硬盘序列号
                try:
                    result = subprocess.run(['wmic', 'diskdrive', 'get', 'serialnumber'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines:
                            if line.strip() and 'SerialNumber' not in line and line.strip() != '(null)':
                                machine_info.append(line.strip())
                                break  # 只取第一个硬盘
                except:
                    pass

            elif os.name == "posix":  # Linux/Mac
                try:
                    # 尝试获取机器ID
                    if os.path.exists('/etc/machine-id'):
                        with open('/etc/machine-id', 'r') as f:
                            machine_info.append(f.read().strip())
                    elif os.path.exists('/var/lib/dbus/machine-id'):
                        with open('/var/lib/dbus/machine-id', 'r') as f:
                            machine_info.append(f.read().strip())
                except:
                    pass

                # 获取CPU信息
                try:
                    with open('/proc/cpuinfo', 'r') as f:
                        for line in f:
                            if 'serial' in line.lower():
                                machine_info.append(line.split(':')[1].strip())
                                break
                except:
                    pass
        except:
            pass

        # 获取MAC地址作为备用
        try:
            mac = uuid.getnode()
            machine_info.append(str(mac))
        except:
            pass

        # 获取系统信息作为额外标识
        try:
            machine_info.append(platform.machine())
            machine_info.append(platform.processor())
        except:
            pass

        # 如果没有获取到任何信息，使用UUID作为备用
        if not machine_info:
            machine_info.append(str(uuid.uuid4()))

        # 组合所有信息并生成哈希
        combined_info = ''.join(machine_info)
        return hashlib.sha256(combined_info.encode()).hexdigest()[:32]  # 取前32位

    def _init_time_validator(self):
        """延迟初始化时间验证器"""
        if self.time_validation_enabled is None:
            try:
                from time_validator import TimeValidator
                self.time_validator = TimeValidator()
                self.time_validation_enabled = True
            except ImportError:
                self.time_validator = None
                self.time_validation_enabled = False

    def _convert_machine_code_to_md5(self, machine_code):
        """
        将机器码转换为独特的MD5值（与转换工具保持一致）
        """
        if not machine_code:
            return ""

        # 第一步：机器码 + 盐值
        step1 = machine_code + self.salt_key

        # 第二步：反转字符串
        step2 = step1[::-1]

        # 第三步：插入额外字符
        step3 = ""
        for i, char in enumerate(step2):
            step3 += char
            if i % 3 == 0:
                step3 += str(i % 10)

        # 第四步：再次加盐并生成MD5
        final_string = step3 + self.salt_key + machine_code
        md5_hash = hashlib.md5(final_string.encode('utf-8')).hexdigest()

        return md5_hash.upper()  # 返回大写MD5

    def _date_to_timestamp(self, date_str):
        """将日期字符串转换为时间戳"""
        try:
            dt = datetime.strptime(date_str, "%Y-%m-%d")
            return int(dt.timestamp())
        except Exception:
            return None

    def is_machine_authorized(self):
        """检查当前机器是否在授权列表中"""
        current_md5 = self._convert_machine_code_to_md5(self.machine_id)
        return current_md5 in self.authorized_config

    def get_machine_code(self):
        """获取当前机器的机器码"""
        return self.machine_id

    def get_machine_md5(self):
        """获取当前机器的MD5值"""
        return self._convert_machine_code_to_md5(self.machine_id)

    def verify_authorization(self):
        """验证机器授权（包含时间检查）"""
        current_md5 = self._convert_machine_code_to_md5(self.machine_id)

        # 1. 检查MD5是否在授权列表中
        if current_md5 not in self.authorized_config:
            return False, f"未授权的机器\n当前机器码: {self.machine_id}\n请联系管理员获取授权"

        # 2. 获取授权配置
        config = self.authorized_config[current_md5]
        expire_date = config.get("expire_date")
        description = config.get("description", "")

        # 3. 时间验证（延迟初始化）
        if expire_date:
            self._init_time_validator()
            if self.time_validation_enabled:
                expire_timestamp = self._date_to_timestamp(expire_date)
                if expire_timestamp:
                    time_valid, time_message = self.time_validator.validate_authorization_time(expire_timestamp)
                    if not time_valid:
                        return False, time_message

                    # 检查是否即将过期（7天内）
                    current_time = int(time.time())
                    days_left = (expire_timestamp - current_time) / (24 * 3600)
                    if days_left <= 7:
                        return True, f"授权验证通过（{description}）\n⚠️ 注意：授权将在{days_left:.0f}天后过期（{expire_date}）"

        return True, f"授权验证通过（{description}）"

class AuthorizationDialog(QWidget):
    def __init__(self, verifier, app_name="YourApp"):
        super().__init__()
        self.verifier = verifier
        self.app_name = app_name
        self.portable_verifier = None
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # 显示当前机器码
        machine_code_label = QLabel(f"当前机器码: {self.verifier.machine_id}")
        machine_code_label.setWordWrap(True)
        layout.addWidget(machine_code_label)

        # 显示授权状态
        valid, message = self.verifier.verify_authorization()
        status_label = QLabel(message)
        status_label.setWordWrap(True)
        layout.addWidget(status_label)

        # 如果未授权，显示联系信息和便携授权码输入
        if not valid:
            contact_label = QLabel("请将上述机器码发送给管理员以获取授权")
            contact_label.setWordWrap(True)
            layout.addWidget(contact_label)

            # 分隔线
            separator = QLabel("─" * 50)
            separator.setAlignment(Qt.AlignCenter)
            layout.addWidget(separator)

            # 便携授权码区域
            portable_label = QLabel("或者输入授权码:")
            portable_label.setWordWrap(True)
            layout.addWidget(portable_label)

            # 便携授权码输入框
            self.portable_input = QLineEdit()
            self.portable_input.setPlaceholderText("请输入授权码...")
            layout.addWidget(self.portable_input)

            # 验证便携授权码按钮
            verify_portable_btn = QPushButton("验证授权码")
            verify_portable_btn.clicked.connect(self.verify_portable_auth)
            layout.addWidget(verify_portable_btn)

            # 便携授权码状态显示
            self.portable_status_label = QLabel("")
            self.portable_status_label.setWordWrap(True)
            self.portable_status_label.setStyleSheet("QLabel { color: blue; }")
            layout.addWidget(self.portable_status_label)

        # 确定按钮
        ok_button = QPushButton("确定")
        ok_button.clicked.connect(self.close)
        layout.addWidget(ok_button)

        self.setLayout(layout)
        self.setWindowTitle("软件授权验证")
        self.setGeometry(300, 300, 500, 400)
        self.setStyleSheet("QLabel { margin: 5px; } QPushButton { margin: 5px; } QLineEdit { margin: 5px; }")

    def verify_portable_auth(self):
        """验证便携授权码"""
        auth_code = self.portable_input.text().strip()
        if not auth_code:
            self.portable_status_label.setText("请输入授权码")
            self.portable_status_label.setStyleSheet("QLabel { color: red; }")
            return

        try:
            # 延迟导入便携授权码验证器
            from portable_auth_verifier import PortableAuthVerifier

            if self.portable_verifier is None:
                self.portable_verifier = PortableAuthVerifier(self.app_name)

            # 验证便携授权码
            valid, auth_data, message = self.portable_verifier.verify_portable_auth_code(auth_code)

            if valid:
                self.portable_status_label.setText(f"✅ {message}")
                self.portable_status_label.setStyleSheet("QLabel { color: green; }")

                # 显示授权信息
                if auth_data:
                    info_text = f"授权有效"
                    if auth_data.get('user'):
                        info_text += f"\n用户: {auth_data['user']}"

                    # 创建成功对话框
                    from PySide6.QtWidgets import QMessageBox
                    msg_box = QMessageBox(self)
                    msg_box.setWindowTitle("授权验证成功")
                    msg_box.setText("授权验证通过！")
                    msg_box.setInformativeText(info_text)
                    msg_box.setIcon(QMessageBox.Information)

                    # 添加启动应用按钮
                    start_btn = msg_box.addButton("启动应用", QMessageBox.AcceptRole)
                    msg_box.addButton("取消", QMessageBox.RejectRole)

                    msg_box.exec()

                    if msg_box.clickedButton() == start_btn:
                        # 关闭授权对话框并启动应用
                        self.accept_and_start_app()
                        return

            else:
                self.portable_status_label.setText(f"❌ {message}")
                self.portable_status_label.setStyleSheet("QLabel { color: red; }")

        except ImportError:
            self.portable_status_label.setText("❌ 授权验证模块未找到")
            self.portable_status_label.setStyleSheet("QLabel { color: red; }")
        except Exception as e:
            self.portable_status_label.setText(f"❌ 验证错误: {str(e)}")
            self.portable_status_label.setStyleSheet("QLabel { color: red; }")

    def accept_and_start_app(self):
        """接受便携授权码并启动应用"""
        # 设置一个标志表示便携授权码验证通过
        self.portable_auth_success = True
        self.close()

def run_application_with_authorization_check(main_app_func, app_name="YourApp"):
    """
    运行带有机器码授权检查的应用程序（增强版，支持便携授权码）

    参数:
    main_app_func: 主应用程序入口函数
    app_name: 应用程序名称
    """
    verifier = MachineCodeVerifier(app_name)

    # 1. 首先检查机器授权
    valid, message = verifier.verify_authorization()
    if valid:
        # 机器授权通过，运行主应用程序
        main_app_func()
        return True

    # 2. 机器授权失败，检查便携授权码
    try:
        from portable_auth_verifier import PortableAuthVerifier
        portable_verifier = PortableAuthVerifier(app_name)

        # 检查是否有有效的便携授权码
        portable_valid, portable_message = portable_verifier.has_valid_portable_auth()
        if portable_valid:
            # 便携授权码有效，运行主应用程序
            print(f"授权验证通过: {portable_message}")
            main_app_func()
            return True
    except ImportError:
        # 便携授权码模块不存在，继续原有流程
        pass
    except Exception as e:
        print(f"便携授权码检查错误: {e}")

    # 3. 两种授权都失败，显示授权对话框
    if PYSIDE6_AVAILABLE:
        # 如果有PySide6，显示GUI对话框（支持便携授权码输入）
        app = QApplication([])
        auth_dialog = AuthorizationDialog(verifier, app_name)
        auth_dialog.show()

        result = app.exec()

        # 检查是否通过便携授权码验证
        if hasattr(auth_dialog, 'portable_auth_success') and auth_dialog.portable_auth_success:
            # 授权验证成功，启动应用
            main_app_func()
            return True

        return result
    else:
        # 如果没有PySide6，打印错误信息
        print("=" * 50)
        print("软件授权验证失败")
        print("=" * 50)
        print(message)
        print("=" * 50)
        print("提示：您可以使用授权码进行授权")
        return False

def check_authorization_only(app_name="YourApp"):
    """
    仅检查授权状态，不启动GUI（用于命令行或测试）
    增强版：支持便携授权码检查

    返回: (是否授权, 消息, 机器码, MD5值)
    """
    verifier = MachineCodeVerifier(app_name)

    # 1. 首先检查机器授权
    valid, message = verifier.verify_authorization()
    if valid:
        return valid, message, verifier.machine_id, verifier.get_machine_md5()

    # 2. 机器授权失败，检查便携授权码
    try:
        from portable_auth_verifier import PortableAuthVerifier
        portable_verifier = PortableAuthVerifier(app_name)

        # 检查是否有有效的便携授权码
        portable_valid, portable_message = portable_verifier.has_valid_portable_auth()
        if portable_valid:
            return True, f"授权验证通过: {portable_message}", verifier.machine_id, verifier.get_machine_md5()
    except ImportError:
        # 便携授权码模块不存在
        pass
    except Exception as e:
        # 便携授权码检查出错，但不影响原有授权流程
        pass

    # 3. 两种授权都失败
    return valid, message, verifier.machine_id, verifier.get_machine_md5()



# 使用示例
if __name__ == "__main__":
    # 定义你的主应用程序函数
    def main_application():
        # 这里是你的主应用程序代码
        from PySide6.QtWidgets import QMainWindow, QLabel

        window = QMainWindow()
        window.setWindowTitle("我的应用")
        window.setGeometry(300, 300, 400, 200)

        label = QLabel("欢迎使用我的应用！")
        window.setCentralWidget(label)
        window.show()

    # 运行带有机器码授权检查的应用
    run_application_with_authorization_check(main_application, "MyApp")
