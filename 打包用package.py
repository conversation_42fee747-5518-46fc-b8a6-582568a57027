import subprocess
import sys
import os

def check_photo_resources():
    """检查photo文件夹资源"""
    print("检查photo文件夹资源...")

    if not os.path.exists('photo'):
        print("❌ photo文件夹不存在")
        return False

    required_files = ['HHlogo.png', 'HHlogo整张水印.png']
    for file in required_files:
        file_path = os.path.join('photo', file)
        if os.path.exists(file_path):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} 不存在")
            return False

    others_dir = os.path.join('photo', 'others')
    if os.path.exists(others_dir):
        others_files = [f for f in os.listdir(others_dir)
                       if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.webp'))]
        print(f"✅ others文件夹 ({len(others_files)} 张图片)")
        if len(others_files) == 0:
            print("⚠️ others文件夹中没有图片文件")
    else:
        print("❌ others文件夹不存在")
        return False

    print("✅ photo资源检查通过")
    return True

def package_project():
    try:
        print("开始打包...")

        # 检查photo资源
        if not check_photo_resources():
            print("❌ photo资源检查失败，请确保photo文件夹完整")
            return

        # 方法1：使用spec文件打包（推荐）
        if os.path.exists('main.spec'):
            print("使用main.spec文件打包...")
            command = ['pyinstaller', 'main.spec']
        else:
            # 方法2：直接命令行打包
            print("使用命令行参数打包...")
            command = [
                'pyinstaller',
                '--onefile',
                '--windowed',  # 重要：确保不显示控制台窗口
                '--noconsole',  # 额外确保不显示控制台
                '--name=视频混剪工具',
            ]

            # 添加图标（如果存在）
            if os.path.exists('icon.ico'):
                command.append('--icon=icon.ico')
                print("✅ 使用图标: icon.ico")
            else:
                print("⚠️ 未找到icon.ico文件")

            # 继续添加其他参数
            command.extend([
                '--hidden-import=utils',
                '--hidden-import=video_mixer',
                '--hidden-import=transition_mixer',
                '--hidden-import=frame_removal',
                '--hidden-import=machine_code_verifier',
                '--hidden-import=machine_code_detector',
                '--hidden-import=time_validator',
                '--hidden-import=machine_code_converter',
                '--hidden-import=startup_optimizer',
                '--hidden-import=authorization_config_generator',
                '--hidden-import=PySide6.QtCore',
                '--hidden-import=PySide6.QtWidgets',
                '--hidden-import=PySide6.QtGui',
                '--hidden-import=resource_manager',
                '--add-data=ui_main_window.py;.' if os.name == 'nt' else '--add-data=ui_main_window.py:.',
                '--add-data=photo;photo' if os.name == 'nt' else '--add-data=photo:photo',
                '--distpath=dist',
                '--workpath=build',
                '--specpath=.',
                'main.py'
            ])

        result = subprocess.run(command, check=True, capture_output=True, text=True)
        print("项目打包成功！")
        print("可执行文件位于: dist/视频混剪工具.exe")
        print("\n重要提示：")
        print("1. 打包后的exe文件不会显示控制台窗口")
        print("2. 所有日志信息都会显示在程序界面中")
        print("3. 确保FFmpeg路径正确配置")
        print("4. photo文件夹已自动包含在exe中，无需额外复制")
        print("5. 水印功能的所有图片资源都已打包完成")

    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    package_project()