# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'M<PERSON>hen视频混剪工具.ui'
##
## Created by: Qt User Interface Compiler version 6.9.0
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (<PERSON>A<PERSON>, QBrush, QColor, QConicalGradient,
    QCursor, QFont, QFontDatabase, QGradient,
    QIcon, QImage, QKeySequence, QLinearGradient,
    QPainter, QPalette, QPixmap, QRadialGradient,
    QTransform)
from PySide6.QtWidgets import (QApplication, QCheckBox, QComboBox, QDoubleSpinBox,
    QFrame, QGroupBox, QLabel, QLineEdit,
    QListView, QMainWindow, QMenu, QMenuBar,
    QPlainTextEdit, QProgressBar, QPushButton, QRadioButton,
    QSizePolicy, QSpinBox, QStatusBar, QTabWidget,
    QTextEdit, QWidget)

class Ui_mainWindow(object):
    def setupUi(self, mainWindow):
        if not mainWindow.objectName():
            mainWindow.setObjectName(u"mainWindow")
        mainWindow.resize(1186, 913)
        self.centralwidget = QWidget(mainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.HJ_tab = QTabWidget(self.centralwidget)
        self.HJ_tab.setObjectName(u"HJ_tab")
        self.HJ_tab.setGeometry(QRect(10, 20, 1171, 841))
        self.HJ_tab.setMinimumSize(QSize(0, 841))
        self.HJ_tab.setStyleSheet(u"/* \u6807\u7b7e\u9875\u57fa\u672c\u6837\u5f0f\uff08\u6240\u6709\u6807\u7b7e\uff09 */\n"
"QTabBar::tab {\n"
"    height: 30px;        /* \u6807\u7b7e\u9875\u9ad8\u5ea6\uff08\u53ef\u6539\uff09 */\n"
"    min-width: 100px;    /* \u6807\u7b7e\u9875\u6700\u5c0f\u5bbd\u5ea6\uff08\u53ef\u6539\uff09 */\n"
"    padding: 8px 15px;   /* \u5185\u8fb9\u8ddd\uff08\u4e0a\u4e0b \u5de6\u53f3\uff0c\u53ef\u6539\uff09 */\n"
"    border: 1px solid #ccc;  /* \u8fb9\u6846\uff08\u53ef\u9009\uff09 */\n"
"    border-bottom: none;     /* \u5e95\u90e8\u65e0\u8fb9\u6846\uff08\u4e0e\u5185\u5bb9\u533a\u5206\uff09 */\n"
"    border-radius: 4px 4px 0 0;  /* \u9876\u90e8\u5706\u89d2\uff08\u53ef\u9009\uff09 */\n"
"    background: #ffffff;    /* \u80cc\u666f\u8272\uff08\u53ef\u9009\uff09 */\n"
"    font-size: 14px;        /* \u5b57\u4f53\u5927\u5c0f\uff08\u53ef\u9009\uff09 */\n"
"}\n"
"\n"
"/* \u9009\u4e2d\u6807\u7b7e\u9875\u7684\u6837\u5f0f\uff08\u53ef\u9009\uff09 */\n"
"QTabBar::tab:selected {\n"
"    background: #48fba1;    /* \u9009\u4e2d\u65f6"
                        "\u80cc\u666f\u8272\uff08\u53ef\u6539\uff09 */\n"
"    font-weight: bold;      /* \u9009\u4e2d\u65f6\u5b57\u4f53\u52a0\u7c97\uff08\u53ef\u9009\uff09 */\n"
"}")
        self.tab_QT = QWidget()
        self.tab_QT.setObjectName(u"tab_QT")
        font = QFont()
        font.setBold(False)
        self.tab_QT.setFont(font)
        self.tab_QT.setStyleSheet(u"/* \u8bbe\u7f6e\u6807\u7b7e\u9875\u7684\u6574\u4f53\u6837\u5f0f */\n"
"QTabBar::tab {\n"
"    height: 50px;        /* \u6807\u7b7e\u9875\u9ad8\u5ea6 */\n"
"    min-width: 80px;     /* \u6807\u7b7e\u9875\u6700\u5c0f\u5bbd\u5ea6 */\n"
"    padding: 5px 10px;   /* \u5185\u8fb9\u8ddd\uff08\u4e0a\u4e0b \u5de6\u53f3\uff09 */\n"
"}")
        self.SPZD_groupBox = QGroupBox(self.tab_QT)
        self.SPZD_groupBox.setObjectName(u"SPZD_groupBox")
        self.SPZD_groupBox.setGeometry(QRect(10, 10, 691, 411))
        self.QT_listView = QListView(self.SPZD_groupBox)
        self.QT_listView.setObjectName(u"QT_listView")
        self.QT_listView.setGeometry(QRect(10, 40, 331, 231))
        self.QT_name = QLabel(self.SPZD_groupBox)
        self.QT_name.setObjectName(u"QT_name")
        self.QT_name.setGeometry(QRect(20, 20, 91, 16))
        self.QT_in = QPushButton(self.SPZD_groupBox)
        self.QT_in.setObjectName(u"QT_in")
        self.QT_in.setGeometry(QRect(10, 280, 111, 41))
        palette = QPalette()
        brush = QBrush(QColor(221, 255, 254, 179))
        brush.setStyle(Qt.BrushStyle.SolidPattern)
        palette.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush)
        palette.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush)
        palette.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush)
        self.QT_in.setPalette(palette)
        self.QT_out = QPushButton(self.SPZD_groupBox)
        self.QT_out.setObjectName(u"QT_out")
        self.QT_out.setGeometry(QRect(120, 280, 111, 41))
        self.QT_clean = QPushButton(self.SPZD_groupBox)
        self.QT_clean.setObjectName(u"QT_clean")
        self.QT_clean.setGeometry(QRect(230, 280, 111, 41))
        self.HT_name_2 = QLabel(self.SPZD_groupBox)
        self.HT_name_2.setObjectName(u"HT_name_2")
        self.HT_name_2.setGeometry(QRect(360, 20, 91, 16))
        self.HT_out = QPushButton(self.SPZD_groupBox)
        self.HT_out.setObjectName(u"HT_out")
        self.HT_out.setGeometry(QRect(460, 280, 111, 41))
        self.HT_clean = QPushButton(self.SPZD_groupBox)
        self.HT_clean.setObjectName(u"HT_clean")
        self.HT_clean.setGeometry(QRect(570, 280, 111, 41))
        self.HT_in = QPushButton(self.SPZD_groupBox)
        self.HT_in.setObjectName(u"HT_in")
        self.HT_in.setGeometry(QRect(350, 280, 111, 41))
        palette1 = QPalette()
        palette1.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush)
        palette1.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush)
        palette1.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush)
        self.HT_in.setPalette(palette1)
        self.HT_listView_2 = QListView(self.SPZD_groupBox)
        self.HT_listView_2.setObjectName(u"HT_listView_2")
        self.HT_listView_2.setGeometry(QRect(350, 40, 331, 231))
        self.SC_name = QLabel(self.SPZD_groupBox)
        self.SC_name.setObjectName(u"SC_name")
        self.SC_name.setGeometry(QRect(20, 340, 91, 16))
        self.SC_lineEdit = QLineEdit(self.SPZD_groupBox)
        self.SC_lineEdit.setObjectName(u"SC_lineEdit")
        self.SC_lineEdit.setGeometry(QRect(20, 360, 271, 31))
        self.SC_where = QPushButton(self.SPZD_groupBox)
        self.SC_where.setObjectName(u"SC_where")
        self.SC_where.setGeometry(QRect(300, 360, 111, 31))
        palette2 = QPalette()
        brush1 = QBrush(QColor(255, 235, 15, 179))
        brush1.setStyle(Qt.BrushStyle.SolidPattern)
        palette2.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush1)
        brush2 = QBrush(QColor(255, 255, 127, 255))
        brush2.setStyle(Qt.BrushStyle.SolidPattern)
        palette2.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Light, brush2)
        palette2.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Midlight, brush2)
        palette2.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush1)
        palette2.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Light, brush2)
        palette2.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Midlight, brush2)
        palette2.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush1)
        palette2.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Light, brush2)
        palette2.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Midlight, brush2)
        self.SC_where.setPalette(palette2)
        self.CSHJC = QPushButton(self.SPZD_groupBox)
        self.CSHJC.setObjectName(u"CSHJC")
        self.CSHJC.setGeometry(QRect(500, 350, 141, 41))
        palette3 = QPalette()
        brush3 = QBrush(QColor(0, 255, 127, 179))
        brush3.setStyle(Qt.BrushStyle.SolidPattern)
        palette3.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush3)
        palette3.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Light, brush2)
        palette3.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Midlight, brush2)
        palette3.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush3)
        palette3.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Light, brush2)
        palette3.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Midlight, brush2)
        palette3.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush3)
        palette3.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Light, brush2)
        palette3.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Midlight, brush2)
        self.CSHJC.setPalette(palette3)
        font1 = QFont()
        font1.setPointSize(11)
        font1.setBold(False)
        self.CSHJC.setFont(font1)
        self.KZ_groupBox = QGroupBox(self.tab_QT)
        self.KZ_groupBox.setObjectName(u"KZ_groupBox")
        self.KZ_groupBox.setGeometry(QRect(10, 430, 691, 331))
        self.CZ_checkBox = QCheckBox(self.KZ_groupBox)
        self.CZ_checkBox.setObjectName(u"CZ_checkBox")
        self.CZ_checkBox.setGeometry(QRect(20, 30, 101, 31))
        font2 = QFont()
        font2.setPointSize(10)
        font2.setBold(True)
        self.CZ_checkBox.setFont(font2)
        self.CZ_label = QLabel(self.KZ_groupBox)
        self.CZ_label.setObjectName(u"CZ_label")
        self.CZ_label.setGeometry(QRect(20, 110, 71, 21))
        self.CZ_label_2 = QLabel(self.KZ_groupBox)
        self.CZ_label_2.setObjectName(u"CZ_label_2")
        self.CZ_label_2.setGeometry(QRect(20, 190, 71, 21))
        self.line_2 = QFrame(self.KZ_groupBox)
        self.line_2.setObjectName(u"line_2")
        self.line_2.setGeometry(QRect(130, 30, 20, 281))
        self.line_2.setFrameShape(QFrame.Shape.VLine)
        self.line_2.setFrameShadow(QFrame.Shadow.Sunken)
        self.ZC_checkBox = QCheckBox(self.KZ_groupBox)
        self.ZC_checkBox.setObjectName(u"ZC_checkBox")
        self.ZC_checkBox.setGeometry(QRect(160, 30, 101, 31))
        self.ZC_checkBox.setFont(font2)
        self.CZ_spinBox_low = QSpinBox(self.KZ_groupBox)
        self.CZ_spinBox_low.setObjectName(u"CZ_spinBox_low")
        self.CZ_spinBox_low.setGeometry(QRect(20, 130, 101, 31))
        self.CZ_spinBox_low.setMinimum(1)
        self.CZ_spinBox_low.setValue(15)
        self.CZ_spinBox_max = QSpinBox(self.KZ_groupBox)
        self.CZ_spinBox_max.setObjectName(u"CZ_spinBox_max")
        self.CZ_spinBox_max.setGeometry(QRect(20, 220, 101, 31))
        self.CZ_spinBox_max.setMinimum(2)
        self.CZ_spinBox_max.setValue(50)
        self.line_3 = QFrame(self.KZ_groupBox)
        self.line_3.setObjectName(u"line_3")
        self.line_3.setGeometry(QRect(510, 30, 20, 281))
        self.line_3.setFrameShape(QFrame.Shape.VLine)
        self.line_3.setFrameShadow(QFrame.Shadow.Sunken)
        self.CMM_checkBox_2 = QCheckBox(self.KZ_groupBox)
        self.CMM_checkBox_2.setObjectName(u"CMM_checkBox_2")
        self.CMM_checkBox_2.setGeometry(QRect(540, 30, 101, 31))
        self.CMM_checkBox_2.setFont(font2)
        self.CMM_label = QLabel(self.KZ_groupBox)
        self.CMM_label.setObjectName(u"CMM_label")
        self.CMM_label.setGeometry(QRect(530, 70, 71, 21))
        self.CMM_spinBox = QSpinBox(self.KZ_groupBox)
        self.CMM_spinBox.setObjectName(u"CMM_spinBox")
        self.CMM_spinBox.setGeometry(QRect(530, 90, 101, 31))
        self.CMM_spinBox.setMinimum(1)
        self.CMM_spinBox.setMaximum(99999)
        self.CMM_label_2 = QLabel(self.KZ_groupBox)
        self.CMM_label_2.setObjectName(u"CMM_label_2")
        self.CMM_label_2.setGeometry(QRect(530, 130, 71, 21))
        self.CMM_lineEdit = QLineEdit(self.KZ_groupBox)
        self.CMM_lineEdit.setObjectName(u"CMM_lineEdit")
        self.CMM_lineEdit.setGeometry(QRect(530, 150, 121, 31))
        self.CMM_label_3 = QLabel(self.KZ_groupBox)
        self.CMM_label_3.setObjectName(u"CMM_label_3")
        self.CMM_label_3.setGeometry(QRect(530, 190, 71, 21))
        self.CMM_lineEdit_2 = QLineEdit(self.KZ_groupBox)
        self.CMM_lineEdit_2.setObjectName(u"CMM_lineEdit_2")
        self.CMM_lineEdit_2.setGeometry(QRect(530, 210, 121, 31))
        self.CMM_checkBox_3 = QCheckBox(self.KZ_groupBox)
        self.CMM_checkBox_3.setObjectName(u"CMM_checkBox_3")
        self.CMM_checkBox_3.setGeometry(QRect(530, 250, 101, 31))
        self.CMM_checkBox_3.setChecked(True)
        self.CMM_checkBox_3.setTristate(False)
        self.spinBox = QSpinBox(self.KZ_groupBox)
        self.spinBox.setObjectName(u"spinBox")
        self.spinBox.setGeometry(QRect(560, 280, 71, 23))
        self.spinBox.setValue(2)
        self.line_4 = QFrame(self.KZ_groupBox)
        self.line_4.setObjectName(u"line_4")
        self.line_4.setGeometry(QRect(150, 136, 351, 20))
        self.line_4.setFrameShape(QFrame.Shape.HLine)
        self.line_4.setFrameShadow(QFrame.Shadow.Sunken)
        self.YY_checkBox_2 = QCheckBox(self.KZ_groupBox)
        self.YY_checkBox_2.setObjectName(u"YY_checkBox_2")
        self.YY_checkBox_2.setGeometry(QRect(160, 160, 151, 31))
        self.YY_checkBox_2.setFont(font2)
        self.YY_pushButton = QPushButton(self.KZ_groupBox)
        self.YY_pushButton.setObjectName(u"YY_pushButton")
        self.YY_pushButton.setGeometry(QRect(400, 200, 101, 31))
        self.YY_lineEdit = QLineEdit(self.KZ_groupBox)
        self.YY_lineEdit.setObjectName(u"YY_lineEdit")
        self.YY_lineEdit.setGeometry(QRect(160, 200, 231, 31))
        self.YY_label = QLabel(self.KZ_groupBox)
        self.YY_label.setObjectName(u"YY_label")
        self.YY_label.setGeometry(QRect(160, 250, 71, 21))
        self.YY_label_2 = QLabel(self.KZ_groupBox)
        self.YY_label_2.setObjectName(u"YY_label_2")
        self.YY_label_2.setGeometry(QRect(320, 250, 71, 21))
        self.YY_spinBox_yspyl = QSpinBox(self.KZ_groupBox)
        self.YY_spinBox_yspyl.setObjectName(u"YY_spinBox_yspyl")
        self.YY_spinBox_yspyl.setGeometry(QRect(160, 280, 81, 31))
        self.YY_spinBox_yspyl.setMinimum(-30)
        self.YY_spinBox_yspyl.setMaximum(15)
        self.YY_spinBox_yspyl.setValue(-5)
        self.YY_spinBox_yyyl = QSpinBox(self.KZ_groupBox)
        self.YY_spinBox_yyyl.setObjectName(u"YY_spinBox_yyyl")
        self.YY_spinBox_yyyl.setGeometry(QRect(320, 280, 81, 31))
        self.YY_spinBox_yyyl.setMinimum(-30)
        self.YY_spinBox_yyyl.setMaximum(15)
        self.YY_spinBox_yyyl.setValue(-10)
        self.YY_label_3 = QLabel(self.KZ_groupBox)
        self.YY_label_3.setObjectName(u"YY_label_3")
        self.YY_label_3.setGeometry(QRect(250, 290, 41, 21))
        self.YY_label_4 = QLabel(self.KZ_groupBox)
        self.YY_label_4.setObjectName(u"YY_label_4")
        self.YY_label_4.setGeometry(QRect(410, 290, 41, 21))
        self.YY_label_5 = QLabel(self.KZ_groupBox)
        self.YY_label_5.setObjectName(u"YY_label_5")
        self.YY_label_5.setGeometry(QRect(440, 250, 61, 71))
        palette4 = QPalette()
        brush4 = QBrush(QColor(254, 0, 4, 228))
        brush4.setStyle(Qt.BrushStyle.SolidPattern)
        palette4.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.WindowText, brush4)
        brush5 = QBrush(QColor(0, 0, 0, 228))
        brush5.setStyle(Qt.BrushStyle.SolidPattern)
        palette4.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Text, brush5)
        palette4.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.ToolTipText, brush5)
        palette4.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.WindowText, brush4)
        palette4.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Text, brush5)
        palette4.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.ToolTipText, brush5)
        palette4.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.ToolTipText, brush5)
        self.YY_label_5.setPalette(palette4)
        self.YY_label_5.setContextMenuPolicy(Qt.ContextMenuPolicy.ActionsContextMenu)
        self.YY_label_5.setWordWrap(True)
        self.ZC_comboBox = QComboBox(self.KZ_groupBox)
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.addItem("")
        self.ZC_comboBox.setObjectName(u"ZC_comboBox")
        self.ZC_comboBox.setGeometry(QRect(170, 70, 231, 41))
        self.ZC_label_10 = QLabel(self.KZ_groupBox)
        self.ZC_label_10.setObjectName(u"ZC_label_10")
        self.ZC_label_10.setGeometry(QRect(420, 50, 71, 21))
        self.ZC_doubleSpinBox = QDoubleSpinBox(self.KZ_groupBox)
        self.ZC_doubleSpinBox.setObjectName(u"ZC_doubleSpinBox")
        self.ZC_doubleSpinBox.setGeometry(QRect(420, 80, 81, 23))
        self.ZC_doubleSpinBox.setDecimals(1)
        self.ZC_doubleSpinBox.setMinimum(0.500000000000000)
        self.ZC_doubleSpinBox.setMaximum(3.000000000000000)
        self.HX_groupBox = QGroupBox(self.tab_QT)
        self.HX_groupBox.setObjectName(u"HX_groupBox")
        self.HX_groupBox.setGeometry(QRect(720, 10, 421, 751))
        self.CL_label = QLabel(self.HX_groupBox)
        self.CL_label.setObjectName(u"CL_label")
        self.CL_label.setGeometry(QRect(20, 30, 81, 16))
        font3 = QFont()
        font3.setPointSize(10)
        font3.setBold(False)
        self.CL_label.setFont(font3)
        self.CL_plainTextEdit = QPlainTextEdit(self.HX_groupBox)
        self.CL_plainTextEdit.setObjectName(u"CL_plainTextEdit")
        self.CL_plainTextEdit.setGeometry(QRect(20, 50, 381, 311))
        self.line = QFrame(self.HX_groupBox)
        self.line.setObjectName(u"line")
        self.line.setGeometry(QRect(20, 370, 381, 16))
        self.line.setFrameShape(QFrame.Shape.HLine)
        self.line.setFrameShadow(QFrame.Shadow.Sunken)
        self.CL_widget = QWidget(self.HX_groupBox)
        self.CL_widget.setObjectName(u"CL_widget")
        self.CL_widget.setGeometry(QRect(20, 410, 381, 231))
        self.CL_label_2 = QLabel(self.HX_groupBox)
        self.CL_label_2.setObjectName(u"CL_label_2")
        self.CL_label_2.setGeometry(QRect(20, 390, 71, 16))
        self.CL_label_2.setFont(font3)
        self.KS_pushButton = QPushButton(self.HX_groupBox)
        self.KS_pushButton.setObjectName(u"KS_pushButton")
        self.KS_pushButton.setGeometry(QRect(100, 700, 141, 41))
        palette5 = QPalette()
        brush6 = QBrush(QColor(0, 255, 255, 179))
        brush6.setStyle(Qt.BrushStyle.SolidPattern)
        palette5.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush6)
        palette5.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush6)
        palette5.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush6)
        self.KS_pushButton.setPalette(palette5)
        self.KS_pushButton.setFont(font2)
        self.TZ_pushButton = QPushButton(self.HX_groupBox)
        self.TZ_pushButton.setObjectName(u"TZ_pushButton")
        self.TZ_pushButton.setGeometry(QRect(260, 700, 131, 41))
        palette6 = QPalette()
        brush7 = QBrush(QColor(255, 0, 4, 179))
        brush7.setStyle(Qt.BrushStyle.SolidPattern)
        palette6.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush7)
        palette6.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush7)
        palette6.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush7)
        self.TZ_pushButton.setPalette(palette6)
        self.TZ_pushButton.setFont(font2)
        self.progressBar = QProgressBar(self.HX_groupBox)
        self.progressBar.setObjectName(u"progressBar")
        self.progressBar.setGeometry(QRect(20, 660, 381, 23))
        font4 = QFont()
        font4.setBold(True)
        self.progressBar.setFont(font4)
        self.progressBar.setValue(0)
        self.HJ_tab.addTab(self.tab_QT, "")
        self.tab_CZ = QWidget()
        self.tab_CZ.setObjectName(u"tab_CZ")
        self.DDCZ_pushButton_2 = QPushButton(self.tab_CZ)
        self.DDCZ_pushButton_2.setObjectName(u"DDCZ_pushButton_2")
        self.DDCZ_pushButton_2.setGeometry(QRect(830, 730, 141, 41))
        palette7 = QPalette()
        palette7.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush6)
        palette7.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush6)
        palette7.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush6)
        self.DDCZ_pushButton_2.setPalette(palette7)
        self.DDCZ_pushButton_2.setFont(font2)
        self.DDCZprogressBar_2 = QProgressBar(self.tab_CZ)
        self.DDCZprogressBar_2.setObjectName(u"DDCZprogressBar_2")
        self.DDCZprogressBar_2.setGeometry(QRect(750, 690, 381, 23))
        self.DDCZprogressBar_2.setFont(font4)
        self.DDCZprogressBar_2.setValue(0)
        self.DDCZ_pushButton_3 = QPushButton(self.tab_CZ)
        self.DDCZ_pushButton_3.setObjectName(u"DDCZ_pushButton_3")
        self.DDCZ_pushButton_3.setGeometry(QRect(990, 730, 131, 41))
        palette8 = QPalette()
        palette8.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush7)
        palette8.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush7)
        palette8.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush7)
        self.DDCZ_pushButton_3.setPalette(palette8)
        self.DDCZ_pushButton_3.setFont(font2)
        self.DDCZ_groupBox_KZ = QGroupBox(self.tab_CZ)
        self.DDCZ_groupBox_KZ.setObjectName(u"DDCZ_groupBox_KZ")
        self.DDCZ_groupBox_KZ.setGeometry(QRect(670, 30, 451, 261))
        self.spinBox_2 = QSpinBox(self.DDCZ_groupBox_KZ)
        self.spinBox_2.setObjectName(u"spinBox_2")
        self.spinBox_2.setGeometry(QRect(250, 200, 71, 23))
        self.spinBox_2.setValue(2)
        self.CMM_checkBox_5 = QCheckBox(self.DDCZ_groupBox_KZ)
        self.CMM_checkBox_5.setObjectName(u"CMM_checkBox_5")
        self.CMM_checkBox_5.setGeometry(QRect(220, 170, 101, 31))
        self.CMM_checkBox_5.setChecked(True)
        self.CMM_checkBox_5.setTristate(False)
        self.CMM_label_6 = QLabel(self.DDCZ_groupBox_KZ)
        self.CMM_label_6.setObjectName(u"CMM_label_6")
        self.CMM_label_6.setGeometry(QRect(210, 100, 71, 21))
        self.CMM_lineEdit_3 = QLineEdit(self.DDCZ_groupBox_KZ)
        self.CMM_lineEdit_3.setObjectName(u"CMM_lineEdit_3")
        self.CMM_lineEdit_3.setGeometry(QRect(210, 60, 151, 31))
        self.CMM_spinBox_2 = QSpinBox(self.DDCZ_groupBox_KZ)
        self.CMM_spinBox_2.setObjectName(u"CMM_spinBox_2")
        self.CMM_spinBox_2.setGeometry(QRect(40, 100, 101, 31))
        self.CMM_spinBox_2.setMinimum(1)
        self.CMM_spinBox_2.setMaximum(99999)
        self.CMM_checkBox_4 = QCheckBox(self.DDCZ_groupBox_KZ)
        self.CMM_checkBox_4.setObjectName(u"CMM_checkBox_4")
        self.CMM_checkBox_4.setGeometry(QRect(40, 40, 101, 31))
        self.CMM_checkBox_4.setFont(font2)
        self.CMM_lineEdit_4 = QLineEdit(self.DDCZ_groupBox_KZ)
        self.CMM_lineEdit_4.setObjectName(u"CMM_lineEdit_4")
        self.CMM_lineEdit_4.setGeometry(QRect(210, 120, 151, 31))
        self.CMM_label_4 = QLabel(self.DDCZ_groupBox_KZ)
        self.CMM_label_4.setObjectName(u"CMM_label_4")
        self.CMM_label_4.setGeometry(QRect(210, 40, 71, 21))
        self.CMM_label_5 = QLabel(self.DDCZ_groupBox_KZ)
        self.CMM_label_5.setObjectName(u"CMM_label_5")
        self.CMM_label_5.setGeometry(QRect(40, 80, 71, 21))
        self.DDCZ_groupBox = QGroupBox(self.tab_CZ)
        self.DDCZ_groupBox.setObjectName(u"DDCZ_groupBox")
        self.DDCZ_groupBox.setGeometry(QRect(40, 30, 591, 731))
        self.DDCZ_spinBox_low_2 = QSpinBox(self.DDCZ_groupBox)
        self.DDCZ_spinBox_low_2.setObjectName(u"DDCZ_spinBox_low_2")
        self.DDCZ_spinBox_low_2.setGeometry(QRect(150, 660, 101, 31))
        self.DDCZ_spinBox_low_2.setMinimum(1)
        self.DDCZ_spinBox_low_2.setValue(15)
        self.DDCZ_spinBox_max_2 = QSpinBox(self.DDCZ_groupBox)
        self.DDCZ_spinBox_max_2.setObjectName(u"DDCZ_spinBox_max_2")
        self.DDCZ_spinBox_max_2.setGeometry(QRect(320, 660, 101, 31))
        self.DDCZ_spinBox_max_2.setMinimum(2)
        self.DDCZ_spinBox_max_2.setValue(50)
        self.DDCZ_name_3 = QLabel(self.DDCZ_groupBox)
        self.DDCZ_name_3.setObjectName(u"DDCZ_name_3")
        self.DDCZ_name_3.setGeometry(QRect(50, 40, 91, 16))
        self.DDCZ_label_3 = QLabel(self.DDCZ_groupBox)
        self.DDCZ_label_3.setObjectName(u"DDCZ_label_3")
        self.DDCZ_label_3.setGeometry(QRect(150, 630, 71, 21))
        self.DDCZ_listView_3 = QListView(self.DDCZ_groupBox)
        self.DDCZ_listView_3.setObjectName(u"DDCZ_listView_3")
        self.DDCZ_listView_3.setGeometry(QRect(40, 60, 361, 271))
        self.DDCZ_in_3 = QPushButton(self.DDCZ_groupBox)
        self.DDCZ_in_3.setObjectName(u"DDCZ_in_3")
        self.DDCZ_in_3.setGeometry(QRect(50, 350, 111, 41))
        palette9 = QPalette()
        palette9.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush)
        palette9.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush)
        palette9.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush)
        self.DDCZ_in_3.setPalette(palette9)
        self.DDCZ_label_4 = QLabel(self.DDCZ_groupBox)
        self.DDCZ_label_4.setObjectName(u"DDCZ_label_4")
        self.DDCZ_label_4.setGeometry(QRect(320, 630, 71, 21))
        self.DDCZ_clean_3 = QPushButton(self.DDCZ_groupBox)
        self.DDCZ_clean_3.setObjectName(u"DDCZ_clean_3")
        self.DDCZ_clean_3.setGeometry(QRect(270, 350, 111, 41))
        self.DDCZ_out_3 = QPushButton(self.DDCZ_groupBox)
        self.DDCZ_out_3.setObjectName(u"DDCZ_out_3")
        self.DDCZ_out_3.setGeometry(QRect(160, 350, 111, 41))
        self.DDCZ_radioButton_TH = QRadioButton(self.DDCZ_groupBox)
        self.DDCZ_radioButton_TH.setObjectName(u"DDCZ_radioButton_TH")
        self.DDCZ_radioButton_TH.setGeometry(QRect(60, 450, 98, 19))
        self.DDCZ_radioButton_ZD = QRadioButton(self.DDCZ_groupBox)
        self.DDCZ_radioButton_ZD.setObjectName(u"DDCZ_radioButton_ZD")
        self.DDCZ_radioButton_ZD.setGeometry(QRect(220, 450, 98, 19))
        self.DDCZ_radioButton_ZD.setChecked(True)
        self.DDCZ_widget_SC = QWidget(self.DDCZ_groupBox)
        self.DDCZ_widget_SC.setObjectName(u"DDCZ_widget_SC")
        self.DDCZ_widget_SC.setGeometry(QRect(40, 500, 501, 81))
        self.DDCZ_where_3 = QPushButton(self.DDCZ_widget_SC)
        self.DDCZ_where_3.setObjectName(u"DDCZ_where_3")
        self.DDCZ_where_3.setGeometry(QRect(380, 30, 111, 31))
        palette10 = QPalette()
        palette10.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush1)
        palette10.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Light, brush2)
        palette10.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Midlight, brush2)
        palette10.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush1)
        palette10.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Light, brush2)
        palette10.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Midlight, brush2)
        palette10.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush1)
        palette10.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Light, brush2)
        palette10.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Midlight, brush2)
        self.DDCZ_where_3.setPalette(palette10)
        self.DDCZ_outname_3 = QLabel(self.DDCZ_widget_SC)
        self.DDCZ_outname_3.setObjectName(u"DDCZ_outname_3")
        self.DDCZ_outname_3.setGeometry(QRect(20, 10, 91, 16))
        self.DDCZ_lineEdit_3 = QLineEdit(self.DDCZ_widget_SC)
        self.DDCZ_lineEdit_3.setObjectName(u"DDCZ_lineEdit_3")
        self.DDCZ_lineEdit_3.setGeometry(QRect(20, 30, 321, 31))
        self.DDCZ_plainTextEdit_2 = QPlainTextEdit(self.tab_CZ)
        self.DDCZ_plainTextEdit_2.setObjectName(u"DDCZ_plainTextEdit_2")
        self.DDCZ_plainTextEdit_2.setGeometry(QRect(670, 340, 451, 311))
        self.DDCZ_label_5 = QLabel(self.tab_CZ)
        self.DDCZ_label_5.setObjectName(u"DDCZ_label_5")
        self.DDCZ_label_5.setGeometry(QRect(670, 320, 81, 16))
        self.DDCZ_label_5.setFont(font3)
        self.HJ_tab.addTab(self.tab_CZ, "")
        self.tab_HJ = QWidget()
        self.tab_HJ.setObjectName(u"tab_HJ")
        self.HJSPZD_groupBox_2 = QGroupBox(self.tab_HJ)
        self.HJSPZD_groupBox_2.setObjectName(u"HJSPZD_groupBox_2")
        self.HJSPZD_groupBox_2.setGeometry(QRect(10, 20, 491, 741))
        self.HJ_listView_4 = QListView(self.HJSPZD_groupBox_2)
        self.HJ_listView_4.setObjectName(u"HJ_listView_4")
        self.HJ_listView_4.setGeometry(QRect(10, 40, 471, 211))
        self.HJ_name_4 = QLabel(self.HJSPZD_groupBox_2)
        self.HJ_name_4.setObjectName(u"HJ_name_4")
        self.HJ_name_4.setGeometry(QRect(20, 20, 91, 16))
        self.HJ_in_4 = QPushButton(self.HJSPZD_groupBox_2)
        self.HJ_in_4.setObjectName(u"HJ_in_4")
        self.HJ_in_4.setGeometry(QRect(100, 270, 111, 41))
        palette11 = QPalette()
        palette11.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush)
        palette11.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush)
        palette11.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush)
        self.HJ_in_4.setPalette(palette11)
        self.HJ_out_4 = QPushButton(self.HJSPZD_groupBox_2)
        self.HJ_out_4.setObjectName(u"HJ_out_4")
        self.HJ_out_4.setGeometry(QRect(210, 270, 111, 41))
        self.HJ_clean_4 = QPushButton(self.HJSPZD_groupBox_2)
        self.HJ_clean_4.setObjectName(u"HJ_clean_4")
        self.HJ_clean_4.setGeometry(QRect(320, 270, 111, 41))
        self.HJSC_name_4 = QLabel(self.HJSPZD_groupBox_2)
        self.HJSC_name_4.setObjectName(u"HJSC_name_4")
        self.HJSC_name_4.setGeometry(QRect(40, 340, 91, 16))
        self.HJSC_lineEdit_4 = QLineEdit(self.HJSPZD_groupBox_2)
        self.HJSC_lineEdit_4.setObjectName(u"HJSC_lineEdit_4")
        self.HJSC_lineEdit_4.setGeometry(QRect(40, 360, 271, 31))
        self.HJSC_where_4 = QPushButton(self.HJSPZD_groupBox_2)
        self.HJSC_where_4.setObjectName(u"HJSC_where_4")
        self.HJSC_where_4.setGeometry(QRect(320, 360, 111, 31))
        palette12 = QPalette()
        palette12.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush1)
        palette12.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Light, brush2)
        palette12.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Midlight, brush2)
        palette12.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush1)
        palette12.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Light, brush2)
        palette12.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Midlight, brush2)
        palette12.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush1)
        palette12.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Light, brush2)
        palette12.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Midlight, brush2)
        self.HJSC_where_4.setPalette(palette12)
        self.CSHJC_4 = QPushButton(self.HJSPZD_groupBox_2)
        self.CSHJC_4.setObjectName(u"CSHJC_4")
        self.CSHJC_4.setGeometry(QRect(320, 680, 141, 41))
        palette13 = QPalette()
        palette13.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush3)
        palette13.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Light, brush2)
        palette13.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Midlight, brush2)
        palette13.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush3)
        palette13.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Light, brush2)
        palette13.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Midlight, brush2)
        palette13.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush3)
        palette13.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Light, brush2)
        palette13.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Midlight, brush2)
        self.CSHJC_4.setPalette(palette13)
        self.CSHJC_4.setFont(font1)
        self.HJ_spinBox_3 = QSpinBox(self.HJSPZD_groupBox_2)
        self.HJ_spinBox_3.setObjectName(u"HJ_spinBox_3")
        self.HJ_spinBox_3.setGeometry(QRect(170, 680, 91, 41))
        self.HJ_spinBox_3.setMinimum(1)
        self.HJ_spinBox_3.setMaximum(9999)
        self.HJ_label = QLabel(self.HJSPZD_groupBox_2)
        self.HJ_label.setObjectName(u"HJ_label")
        self.HJ_label.setGeometry(QRect(20, 690, 131, 31))
        self.HJ_pushButton_GJ = QPushButton(self.HJSPZD_groupBox_2)
        self.HJ_pushButton_GJ.setObjectName(u"HJ_pushButton_GJ")
        self.HJ_pushButton_GJ.setGeometry(QRect(30, 420, 111, 31))
        palette14 = QPalette()
        brush8 = QBrush(QColor(170, 170, 255, 228))
        brush8.setStyle(Qt.BrushStyle.SolidPattern)
        palette14.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.WindowText, brush8)
        brush9 = QBrush(QColor(255, 255, 255, 0))
        brush9.setStyle(Qt.BrushStyle.SolidPattern)
        palette14.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush9)
        brush10 = QBrush(QColor(170, 0, 255, 228))
        brush10.setStyle(Qt.BrushStyle.SolidPattern)
        palette14.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Text, brush10)
        palette14.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.WindowText, brush8)
        palette14.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush9)
        palette14.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Text, brush10)
        palette14.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush9)
        self.HJ_pushButton_GJ.setPalette(palette14)
        self.HJ_label_GJ = QLabel(self.HJSPZD_groupBox_2)
        self.HJ_label_GJ.setObjectName(u"HJ_label_GJ")
        self.HJ_label_GJ.setGeometry(QRect(160, 420, 311, 31))
        self.HJ_label_GJ.setWordWrap(False)
        self.HJ_label_GJ.setOpenExternalLinks(False)
        self.HJ_groupBox_GJ = QGroupBox(self.HJSPZD_groupBox_2)
        self.HJ_groupBox_GJ.setObjectName(u"HJ_groupBox_GJ")
        self.HJ_groupBox_GJ.setGeometry(QRect(20, 460, 451, 211))
        self.GJ_label_PDmin = QLabel(self.HJ_groupBox_GJ)
        self.GJ_label_PDmin.setObjectName(u"GJ_label_PDmin")
        self.GJ_label_PDmin.setGeometry(QRect(20, 30, 101, 21))
        self.GJ_spinBox_PDmin = QSpinBox(self.HJ_groupBox_GJ)
        self.GJ_spinBox_PDmin.setObjectName(u"GJ_spinBox_PDmin")
        self.GJ_spinBox_PDmin.setGeometry(QRect(130, 30, 61, 23))
        self.GJ_spinBox_PDmin.setMinimum(1)
        self.GJ_spinBox_PDmin.setMaximum(15)
        self.GJ_spinBox_PDmin.setValue(3)
        self.GJ_spinBox_PDmax = QSpinBox(self.HJ_groupBox_GJ)
        self.GJ_spinBox_PDmax.setObjectName(u"GJ_spinBox_PDmax")
        self.GJ_spinBox_PDmax.setGeometry(QRect(340, 30, 61, 23))
        self.GJ_spinBox_PDmax.setMinimum(1)
        self.GJ_spinBox_PDmax.setMaximum(15)
        self.GJ_spinBox_PDmax.setValue(5)
        self.GJ_label_PDmax = QLabel(self.HJ_groupBox_GJ)
        self.GJ_label_PDmax.setObjectName(u"GJ_label_PDmax")
        self.GJ_label_PDmax.setGeometry(QRect(230, 30, 101, 21))
        self.GJ_spinBox_ZSCmin = QSpinBox(self.HJ_groupBox_GJ)
        self.GJ_spinBox_ZSCmin.setObjectName(u"GJ_spinBox_ZSCmin")
        self.GJ_spinBox_ZSCmin.setGeometry(QRect(130, 80, 61, 23))
        self.GJ_spinBox_ZSCmin.setMinimum(1)
        self.GJ_spinBox_ZSCmin.setMaximum(50)
        self.GJ_spinBox_ZSCmin.setValue(15)
        self.GJ_label_ZSCmin = QLabel(self.HJ_groupBox_GJ)
        self.GJ_label_ZSCmin.setObjectName(u"GJ_label_ZSCmin")
        self.GJ_label_ZSCmin.setGeometry(QRect(20, 80, 101, 21))
        self.GJ_label_ZSCmax = QLabel(self.HJ_groupBox_GJ)
        self.GJ_label_ZSCmax.setObjectName(u"GJ_label_ZSCmax")
        self.GJ_label_ZSCmax.setGeometry(QRect(230, 80, 101, 21))
        self.GJ_spinBox_ZSCmax = QSpinBox(self.HJ_groupBox_GJ)
        self.GJ_spinBox_ZSCmax.setObjectName(u"GJ_spinBox_ZSCmax")
        self.GJ_spinBox_ZSCmax.setGeometry(QRect(340, 80, 61, 23))
        self.GJ_spinBox_ZSCmax.setMinimum(2)
        self.GJ_spinBox_ZSCmax.setMaximum(60)
        self.GJ_spinBox_ZSCmax.setValue(31)
        self.GJ_checkBox = QCheckBox(self.HJ_groupBox_GJ)
        self.GJ_checkBox.setObjectName(u"GJ_checkBox")
        self.GJ_checkBox.setGeometry(QRect(20, 130, 201, 31))
        self.GJ_checkBox_2 = QCheckBox(self.HJ_groupBox_GJ)
        self.GJ_checkBox_2.setObjectName(u"GJ_checkBox_2")
        self.GJ_checkBox_2.setGeometry(QRect(20, 170, 201, 31))
        self.HJHX_groupBox_2 = QGroupBox(self.tab_HJ)
        self.HJHX_groupBox_2.setObjectName(u"HJHX_groupBox_2")
        self.HJHX_groupBox_2.setGeometry(QRect(740, 350, 421, 411))
        self.HJCL_label_3 = QLabel(self.HJHX_groupBox_2)
        self.HJCL_label_3.setObjectName(u"HJCL_label_3")
        self.HJCL_label_3.setGeometry(QRect(20, 30, 81, 16))
        self.HJCL_label_3.setFont(font3)
        self.CL_plainTextEdit_2 = QPlainTextEdit(self.HJHX_groupBox_2)
        self.CL_plainTextEdit_2.setObjectName(u"CL_plainTextEdit_2")
        self.CL_plainTextEdit_2.setGeometry(QRect(20, 50, 381, 241))
        self.HJKS_pushButton_2 = QPushButton(self.HJHX_groupBox_2)
        self.HJKS_pushButton_2.setObjectName(u"HJKS_pushButton_2")
        self.HJKS_pushButton_2.setGeometry(QRect(80, 350, 161, 41))
        palette15 = QPalette()
        palette15.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush6)
        palette15.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush6)
        palette15.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush6)
        self.HJKS_pushButton_2.setPalette(palette15)
        self.HJKS_pushButton_2.setFont(font2)
        self.HJTZ_pushButton_2 = QPushButton(self.HJHX_groupBox_2)
        self.HJTZ_pushButton_2.setObjectName(u"HJTZ_pushButton_2")
        self.HJTZ_pushButton_2.setGeometry(QRect(260, 350, 131, 41))
        palette16 = QPalette()
        palette16.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush7)
        palette16.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush7)
        palette16.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush7)
        self.HJTZ_pushButton_2.setPalette(palette16)
        self.HJTZ_pushButton_2.setFont(font2)
        self.HJ_progressBar_2 = QProgressBar(self.HJHX_groupBox_2)
        self.HJ_progressBar_2.setObjectName(u"HJ_progressBar_2")
        self.HJ_progressBar_2.setGeometry(QRect(20, 310, 381, 23))
        self.HJ_progressBar_2.setFont(font4)
        self.HJ_progressBar_2.setValue(0)
        self.HJ_groupBox_HJZC = QGroupBox(self.tab_HJ)
        self.HJ_groupBox_HJZC.setObjectName(u"HJ_groupBox_HJZC")
        self.HJ_groupBox_HJZC.setGeometry(QRect(740, 20, 421, 131))
        self.HJ_checkBox_ZC = QCheckBox(self.HJ_groupBox_HJZC)
        self.HJ_checkBox_ZC.setObjectName(u"HJ_checkBox_ZC")
        self.HJ_checkBox_ZC.setGeometry(QRect(20, 30, 101, 31))
        self.HJ_checkBox_ZC.setFont(font2)
        self.HJ_comboBox_ZC = QComboBox(self.HJ_groupBox_HJZC)
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.addItem("")
        self.HJ_comboBox_ZC.setObjectName(u"HJ_comboBox_ZC")
        self.HJ_comboBox_ZC.setGeometry(QRect(20, 70, 231, 41))
        self.HJ_radioButton_y = QRadioButton(self.HJ_groupBox_HJZC)
        self.HJ_radioButton_y.setObjectName(u"HJ_radioButton_y")
        self.HJ_radioButton_y.setGeometry(QRect(280, 40, 98, 19))
        self.HJ_radioButton_n = QRadioButton(self.HJ_groupBox_HJZC)
        self.HJ_radioButton_n.setObjectName(u"HJ_radioButton_n")
        self.HJ_radioButton_n.setGeometry(QRect(280, 80, 98, 19))
        self.ZC_doubleSpinBox_2 = QDoubleSpinBox(self.HJ_groupBox_HJZC)
        self.ZC_doubleSpinBox_2.setObjectName(u"ZC_doubleSpinBox_2")
        self.ZC_doubleSpinBox_2.setGeometry(QRect(190, 30, 71, 23))
        self.ZC_doubleSpinBox_2.setDecimals(1)
        self.ZC_doubleSpinBox_2.setMinimum(0.500000000000000)
        self.ZC_doubleSpinBox_2.setMaximum(3.000000000000000)
        self.ZC_doubleSpinBox_2.setValue(1.000000000000000)
        self.ZC_label_11 = QLabel(self.HJ_groupBox_HJZC)
        self.ZC_label_11.setObjectName(u"ZC_label_11")
        self.ZC_label_11.setGeometry(QRect(120, 30, 61, 21))
        self.KZ_groupBox_2 = QGroupBox(self.tab_HJ)
        self.KZ_groupBox_2.setObjectName(u"KZ_groupBox_2")
        self.KZ_groupBox_2.setGeometry(QRect(510, 20, 221, 741))
        self.CZ_checkBox_2 = QCheckBox(self.KZ_groupBox_2)
        self.CZ_checkBox_2.setObjectName(u"CZ_checkBox_2")
        self.CZ_checkBox_2.setGeometry(QRect(20, 30, 101, 31))
        self.CZ_checkBox_2.setFont(font2)
        self.CZ_label_3 = QLabel(self.KZ_groupBox_2)
        self.CZ_label_3.setObjectName(u"CZ_label_3")
        self.CZ_label_3.setGeometry(QRect(20, 70, 71, 21))
        self.CZ_label_4 = QLabel(self.KZ_groupBox_2)
        self.CZ_label_4.setObjectName(u"CZ_label_4")
        self.CZ_label_4.setGeometry(QRect(20, 150, 71, 21))
        self.CZ_spinBox_low_2 = QSpinBox(self.KZ_groupBox_2)
        self.CZ_spinBox_low_2.setObjectName(u"CZ_spinBox_low_2")
        self.CZ_spinBox_low_2.setGeometry(QRect(20, 90, 101, 31))
        self.CZ_spinBox_low_2.setMinimum(1)
        self.CZ_spinBox_low_2.setValue(15)
        self.CZ_spinBox_max_2 = QSpinBox(self.KZ_groupBox_2)
        self.CZ_spinBox_max_2.setObjectName(u"CZ_spinBox_max_2")
        self.CZ_spinBox_max_2.setGeometry(QRect(20, 180, 101, 31))
        self.CZ_spinBox_max_2.setMinimum(2)
        self.CZ_spinBox_max_2.setValue(50)
        self.CMM_checkBox_6 = QCheckBox(self.KZ_groupBox_2)
        self.CMM_checkBox_6.setObjectName(u"CMM_checkBox_6")
        self.CMM_checkBox_6.setGeometry(QRect(20, 500, 101, 31))
        self.CMM_checkBox_6.setFont(font2)
        self.CMM_label_7 = QLabel(self.KZ_groupBox_2)
        self.CMM_label_7.setObjectName(u"CMM_label_7")
        self.CMM_label_7.setGeometry(QRect(30, 540, 71, 21))
        self.CMM_spinBox_3 = QSpinBox(self.KZ_groupBox_2)
        self.CMM_spinBox_3.setObjectName(u"CMM_spinBox_3")
        self.CMM_spinBox_3.setGeometry(QRect(100, 530, 71, 31))
        self.CMM_spinBox_3.setMinimum(1)
        self.CMM_spinBox_3.setMaximum(99999)
        self.CMM_label_8 = QLabel(self.KZ_groupBox_2)
        self.CMM_label_8.setObjectName(u"CMM_label_8")
        self.CMM_label_8.setGeometry(QRect(30, 570, 71, 21))
        self.CMM_lineEdit_5 = QLineEdit(self.KZ_groupBox_2)
        self.CMM_lineEdit_5.setObjectName(u"CMM_lineEdit_5")
        self.CMM_lineEdit_5.setGeometry(QRect(30, 590, 121, 31))
        self.CMM_label_9 = QLabel(self.KZ_groupBox_2)
        self.CMM_label_9.setObjectName(u"CMM_label_9")
        self.CMM_label_9.setGeometry(QRect(30, 630, 71, 21))
        self.CMM_lineEdit_6 = QLineEdit(self.KZ_groupBox_2)
        self.CMM_lineEdit_6.setObjectName(u"CMM_lineEdit_6")
        self.CMM_lineEdit_6.setGeometry(QRect(30, 650, 121, 31))
        self.CMM_checkBox_7 = QCheckBox(self.KZ_groupBox_2)
        self.CMM_checkBox_7.setObjectName(u"CMM_checkBox_7")
        self.CMM_checkBox_7.setGeometry(QRect(30, 700, 101, 31))
        self.CMM_checkBox_7.setChecked(True)
        self.CMM_checkBox_7.setTristate(False)
        self.spinBox_3 = QSpinBox(self.KZ_groupBox_2)
        self.spinBox_3.setObjectName(u"spinBox_3")
        self.spinBox_3.setGeometry(QRect(140, 700, 61, 23))
        self.spinBox_3.setValue(2)
        self.line_7 = QFrame(self.KZ_groupBox_2)
        self.line_7.setObjectName(u"line_7")
        self.line_7.setGeometry(QRect(10, 220, 201, 20))
        self.line_7.setLineWidth(2)
        self.line_7.setFrameShape(QFrame.Shape.HLine)
        self.line_7.setFrameShadow(QFrame.Shadow.Sunken)
        self.YY_checkBox_3 = QCheckBox(self.KZ_groupBox_2)
        self.YY_checkBox_3.setObjectName(u"YY_checkBox_3")
        self.YY_checkBox_3.setGeometry(QRect(20, 240, 151, 31))
        self.YY_checkBox_3.setFont(font2)
        self.YY_pushButton_2 = QPushButton(self.KZ_groupBox_2)
        self.YY_pushButton_2.setObjectName(u"YY_pushButton_2")
        self.YY_pushButton_2.setGeometry(QRect(60, 320, 101, 31))
        palette17 = QPalette()
        brush11 = QBrush(QColor(255, 170, 255, 179))
        brush11.setStyle(Qt.BrushStyle.SolidPattern)
        palette17.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush11)
        palette17.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush11)
        palette17.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush11)
        self.YY_pushButton_2.setPalette(palette17)
        self.YY_lineEdit_2 = QLineEdit(self.KZ_groupBox_2)
        self.YY_lineEdit_2.setObjectName(u"YY_lineEdit_2")
        self.YY_lineEdit_2.setGeometry(QRect(20, 280, 191, 31))
        self.YY_label_6 = QLabel(self.KZ_groupBox_2)
        self.YY_label_6.setObjectName(u"YY_label_6")
        self.YY_label_6.setGeometry(QRect(20, 360, 71, 21))
        self.YY_label_7 = QLabel(self.KZ_groupBox_2)
        self.YY_label_7.setObjectName(u"YY_label_7")
        self.YY_label_7.setGeometry(QRect(120, 360, 71, 21))
        self.YY_spinBox_yspyl_2 = QSpinBox(self.KZ_groupBox_2)
        self.YY_spinBox_yspyl_2.setObjectName(u"YY_spinBox_yspyl_2")
        self.YY_spinBox_yspyl_2.setGeometry(QRect(10, 390, 71, 31))
        self.YY_spinBox_yspyl_2.setMinimum(-30)
        self.YY_spinBox_yspyl_2.setMaximum(15)
        self.YY_spinBox_yspyl_2.setValue(-5)
        self.YY_spinBox_yyyl_2 = QSpinBox(self.KZ_groupBox_2)
        self.YY_spinBox_yyyl_2.setObjectName(u"YY_spinBox_yyyl_2")
        self.YY_spinBox_yyyl_2.setGeometry(QRect(120, 390, 71, 31))
        self.YY_spinBox_yyyl_2.setMinimum(-30)
        self.YY_spinBox_yyyl_2.setMaximum(15)
        self.YY_spinBox_yyyl_2.setValue(-10)
        self.YY_label_8 = QLabel(self.KZ_groupBox_2)
        self.YY_label_8.setObjectName(u"YY_label_8")
        self.YY_label_8.setGeometry(QRect(90, 400, 21, 21))
        self.YY_label_9 = QLabel(self.KZ_groupBox_2)
        self.YY_label_9.setObjectName(u"YY_label_9")
        self.YY_label_9.setGeometry(QRect(200, 400, 21, 21))
        self.YY_label_10 = QLabel(self.KZ_groupBox_2)
        self.YY_label_10.setObjectName(u"YY_label_10")
        self.YY_label_10.setGeometry(QRect(20, 430, 201, 31))
        palette18 = QPalette()
        palette18.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.WindowText, brush4)
        palette18.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Text, brush5)
        palette18.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.ToolTipText, brush5)
        palette18.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.WindowText, brush4)
        palette18.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Text, brush5)
        palette18.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.ToolTipText, brush5)
        palette18.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.ToolTipText, brush5)
        self.YY_label_10.setPalette(palette18)
        self.YY_label_10.setContextMenuPolicy(Qt.ContextMenuPolicy.ActionsContextMenu)
        self.YY_label_10.setWordWrap(True)
        self.line_8 = QFrame(self.KZ_groupBox_2)
        self.line_8.setObjectName(u"line_8")
        self.line_8.setGeometry(QRect(10, 470, 201, 20))
        self.line_8.setLineWidth(2)
        self.line_8.setFrameShape(QFrame.Shape.HLine)
        self.line_8.setFrameShadow(QFrame.Shadow.Sunken)
        self.HJ_groupBox_LK = QGroupBox(self.tab_HJ)
        self.HJ_groupBox_LK.setObjectName(u"HJ_groupBox_LK")
        self.HJ_groupBox_LK.setGeometry(QRect(740, 160, 421, 181))
        self.HJ_checkBox_LK = QCheckBox(self.HJ_groupBox_LK)
        self.HJ_checkBox_LK.setObjectName(u"HJ_checkBox_LK")
        self.HJ_checkBox_LK.setGeometry(QRect(20, 30, 101, 31))
        self.HJ_checkBox_LK.setFont(font2)
        self.LK_lineEdit_3 = QLineEdit(self.HJ_groupBox_LK)
        self.LK_lineEdit_3.setObjectName(u"LK_lineEdit_3")
        self.LK_lineEdit_3.setGeometry(QRect(130, 60, 161, 31))
        self.LK_pushButton_3 = QPushButton(self.HJ_groupBox_LK)
        self.LK_pushButton_3.setObjectName(u"LK_pushButton_3")
        self.LK_pushButton_3.setGeometry(QRect(300, 60, 101, 31))
        self.LK_radioButton_duo = QRadioButton(self.HJ_groupBox_LK)
        self.LK_radioButton_duo.setObjectName(u"LK_radioButton_duo")
        self.LK_radioButton_duo.setGeometry(QRect(20, 70, 91, 19))
        self.LK_radioButton_dan = QRadioButton(self.HJ_groupBox_LK)
        self.LK_radioButton_dan.setObjectName(u"LK_radioButton_dan")
        self.LK_radioButton_dan.setGeometry(QRect(20, 130, 91, 19))
        self.LK_listView = QListView(self.HJ_groupBox_LK)
        self.LK_listView.setObjectName(u"LK_listView")
        self.LK_listView.setGeometry(QRect(90, 110, 191, 51))
        self.LK_in_5 = QPushButton(self.HJ_groupBox_LK)
        self.LK_in_5.setObjectName(u"LK_in_5")
        self.LK_in_5.setGeometry(QRect(300, 120, 51, 41))
        palette19 = QPalette()
        palette19.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush)
        palette19.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush)
        palette19.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush)
        self.LK_in_5.setPalette(palette19)
        self.LK_clean_5 = QPushButton(self.HJ_groupBox_LK)
        self.LK_clean_5.setObjectName(u"LK_clean_5")
        self.LK_clean_5.setGeometry(QRect(360, 120, 51, 41))
        self.LK_label = QLabel(self.HJ_groupBox_LK)
        self.LK_label.setObjectName(u"LK_label")
        self.LK_label.setGeometry(QRect(160, 20, 171, 21))
        palette20 = QPalette()
        brush12 = QBrush(QColor(255, 0, 0, 228))
        brush12.setStyle(Qt.BrushStyle.SolidPattern)
        palette20.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.WindowText, brush12)
        palette20.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Text, brush5)
        palette20.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.WindowText, brush12)
        palette20.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Text, brush5)
        self.LK_label.setPalette(palette20)
        self.HJ_tab.addTab(self.tab_HJ, "")
        self.XSC_tab = QWidget()
        self.XSC_tab.setObjectName(u"XSC_tab")
        self.XSC_groupBox_2 = QGroupBox(self.XSC_tab)
        self.XSC_groupBox_2.setObjectName(u"XSC_groupBox_2")
        self.XSC_groupBox_2.setGeometry(QRect(10, 20, 551, 751))
        self.XSC_name_4 = QLabel(self.XSC_groupBox_2)
        self.XSC_name_4.setObjectName(u"XSC_name_4")
        self.XSC_name_4.setGeometry(QRect(50, 40, 91, 16))
        self.XSC_listView_4 = QListView(self.XSC_groupBox_2)
        self.XSC_listView_4.setObjectName(u"XSC_listView_4")
        self.XSC_listView_4.setGeometry(QRect(40, 60, 361, 271))
        self.XSC_in_4 = QPushButton(self.XSC_groupBox_2)
        self.XSC_in_4.setObjectName(u"XSC_in_4")
        self.XSC_in_4.setGeometry(QRect(50, 350, 111, 41))
        palette21 = QPalette()
        palette21.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush)
        palette21.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush)
        palette21.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush)
        self.XSC_in_4.setPalette(palette21)
        self.XSC_clean_4 = QPushButton(self.XSC_groupBox_2)
        self.XSC_clean_4.setObjectName(u"XSC_clean_4")
        self.XSC_clean_4.setGeometry(QRect(270, 350, 111, 41))
        self.XSC_out_4 = QPushButton(self.XSC_groupBox_2)
        self.XSC_out_4.setObjectName(u"XSC_out_4")
        self.XSC_out_4.setGeometry(QRect(160, 350, 111, 41))
        self.XSC_radioButton_TH_2 = QRadioButton(self.XSC_groupBox_2)
        self.XSC_radioButton_TH_2.setObjectName(u"XSC_radioButton_TH_2")
        self.XSC_radioButton_TH_2.setGeometry(QRect(60, 450, 98, 19))
        self.XSC_radioButton_TH_2.setChecked(True)
        self.XSC_radioButton_ZD_2 = QRadioButton(self.XSC_groupBox_2)
        self.XSC_radioButton_ZD_2.setObjectName(u"XSC_radioButton_ZD_2")
        self.XSC_radioButton_ZD_2.setGeometry(QRect(220, 450, 98, 19))
        self.XSC_radioButton_ZD_2.setChecked(False)
        self.XSC_widget_SC_2 = QWidget(self.XSC_groupBox_2)
        self.XSC_widget_SC_2.setObjectName(u"XSC_widget_SC_2")
        self.XSC_widget_SC_2.setGeometry(QRect(40, 500, 501, 81))
        self.XSC_where_4 = QPushButton(self.XSC_widget_SC_2)
        self.XSC_where_4.setObjectName(u"XSC_where_4")
        self.XSC_where_4.setGeometry(QRect(380, 30, 111, 31))
        palette22 = QPalette()
        palette22.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush1)
        palette22.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Light, brush2)
        palette22.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Midlight, brush2)
        palette22.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush1)
        palette22.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Light, brush2)
        palette22.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Midlight, brush2)
        palette22.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush1)
        palette22.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Light, brush2)
        palette22.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Midlight, brush2)
        self.XSC_where_4.setPalette(palette22)
        self.XSC_outname_4 = QLabel(self.XSC_widget_SC_2)
        self.XSC_outname_4.setObjectName(u"XSC_outname_4")
        self.XSC_outname_4.setGeometry(QRect(20, 10, 91, 16))
        self.XSC_lineEdit_4 = QLineEdit(self.XSC_widget_SC_2)
        self.XSC_lineEdit_4.setObjectName(u"XSC_lineEdit_4")
        self.XSC_lineEdit_4.setGeometry(QRect(20, 30, 321, 31))
        self.XSC_label = QLabel(self.XSC_groupBox_2)
        self.XSC_label.setObjectName(u"XSC_label")
        self.XSC_label.setGeometry(QRect(60, 610, 451, 111))
        font5 = QFont()
        font5.setPointSize(10)
        self.XSC_label.setFont(font5)
        self.XSC_groupBox_FBL = QGroupBox(self.XSC_tab)
        self.XSC_groupBox_FBL.setObjectName(u"XSC_groupBox_FBL")
        self.XSC_groupBox_FBL.setGeometry(QRect(580, 20, 561, 91))
        self.W_label = QLabel(self.XSC_groupBox_FBL)
        self.W_label.setObjectName(u"W_label")
        self.W_label.setGeometry(QRect(20, 40, 91, 31))
        font6 = QFont()
        font6.setPointSize(11)
        self.W_label.setFont(font6)
        self.SXC_textEdit_W = QTextEdit(self.XSC_groupBox_FBL)
        self.SXC_textEdit_W.setObjectName(u"SXC_textEdit_W")
        self.SXC_textEdit_W.setGeometry(QRect(70, 40, 111, 31))
        self.H_label = QLabel(self.XSC_groupBox_FBL)
        self.H_label.setObjectName(u"H_label")
        self.H_label.setGeometry(QRect(220, 40, 91, 31))
        self.H_label.setFont(font6)
        self.SXC_textEdit_H = QTextEdit(self.XSC_groupBox_FBL)
        self.SXC_textEdit_H.setObjectName(u"SXC_textEdit_H")
        self.SXC_textEdit_H.setGeometry(QRect(270, 40, 111, 31))
        self.label = QLabel(self.XSC_groupBox_FBL)
        self.label.setObjectName(u"label")
        self.label.setGeometry(QRect(440, 40, 71, 31))
        self.label.setFont(font4)
        self.XSC_groupBox_BTL = QGroupBox(self.XSC_tab)
        self.XSC_groupBox_BTL.setObjectName(u"XSC_groupBox_BTL")
        self.XSC_groupBox_BTL.setGeometry(QRect(580, 130, 561, 91))
        self.BTL_label = QLabel(self.XSC_groupBox_BTL)
        self.BTL_label.setObjectName(u"BTL_label")
        self.BTL_label.setGeometry(QRect(180, 30, 361, 41))
        self.BTL_label.setFont(font6)
        self.SXC_textEdit_BTL = QTextEdit(self.XSC_groupBox_BTL)
        self.SXC_textEdit_BTL.setObjectName(u"SXC_textEdit_BTL")
        self.SXC_textEdit_BTL.setGeometry(QRect(30, 40, 121, 31))
        self.XSC_groupBox_3KS = QGroupBox(self.XSC_tab)
        self.XSC_groupBox_3KS.setObjectName(u"XSC_groupBox_3KS")
        self.XSC_groupBox_3KS.setGeometry(QRect(580, 240, 561, 531))
        self.XSC_label_4RZ = QLabel(self.XSC_groupBox_3KS)
        self.XSC_label_4RZ.setObjectName(u"XSC_label_4RZ")
        self.XSC_label_4RZ.setGeometry(QRect(20, 30, 81, 16))
        self.XSC_label_4RZ.setFont(font3)
        self.XSC_plainTextEdit_3RZ = QPlainTextEdit(self.XSC_groupBox_3KS)
        self.XSC_plainTextEdit_3RZ.setObjectName(u"XSC_plainTextEdit_3RZ")
        self.XSC_plainTextEdit_3RZ.setGeometry(QRect(20, 50, 511, 351))
        self.XSC_pushButton_3 = QPushButton(self.XSC_groupBox_3KS)
        self.XSC_pushButton_3.setObjectName(u"XSC_pushButton_3")
        self.XSC_pushButton_3.setGeometry(QRect(110, 470, 201, 41))
        palette23 = QPalette()
        palette23.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush6)
        palette23.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush6)
        palette23.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush6)
        self.XSC_pushButton_3.setPalette(palette23)
        self.XSC_pushButton_3.setFont(font2)
        self.XSC_pushButton_4 = QPushButton(self.XSC_groupBox_3KS)
        self.XSC_pushButton_4.setObjectName(u"XSC_pushButton_4")
        self.XSC_pushButton_4.setGeometry(QRect(330, 470, 191, 41))
        palette24 = QPalette()
        palette24.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush7)
        palette24.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush7)
        palette24.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush7)
        self.XSC_pushButton_4.setPalette(palette24)
        self.XSC_pushButton_4.setFont(font2)
        self.XSC_progressBar_3 = QProgressBar(self.XSC_groupBox_3KS)
        self.XSC_progressBar_3.setObjectName(u"XSC_progressBar_3")
        self.XSC_progressBar_3.setGeometry(QRect(150, 430, 381, 23))
        self.XSC_progressBar_3.setFont(font4)
        self.XSC_progressBar_3.setValue(0)
        self.HJ_tab.addTab(self.XSC_tab, "")
        self.tab_SY = QWidget()
        self.tab_SY.setObjectName(u"tab_SY")
        self.SY_groupBox_3 = QGroupBox(self.tab_SY)
        self.SY_groupBox_3.setObjectName(u"SY_groupBox_3")
        self.SY_groupBox_3.setGeometry(QRect(10, 20, 551, 751))
        self.SY_name_5 = QLabel(self.SY_groupBox_3)
        self.SY_name_5.setObjectName(u"SY_name_5")
        self.SY_name_5.setGeometry(QRect(50, 40, 201, 16))
        self.SY_listView_5 = QListView(self.SY_groupBox_3)
        self.SY_listView_5.setObjectName(u"SY_listView_5")
        self.SY_listView_5.setGeometry(QRect(40, 60, 361, 271))
        self.SY_in_5 = QPushButton(self.SY_groupBox_3)
        self.SY_in_5.setObjectName(u"SY_in_5")
        self.SY_in_5.setGeometry(QRect(50, 350, 111, 41))
        palette25 = QPalette()
        palette25.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush)
        palette25.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush)
        palette25.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush)
        self.SY_in_5.setPalette(palette25)
        self.SY_clean_5 = QPushButton(self.SY_groupBox_3)
        self.SY_clean_5.setObjectName(u"SY_clean_5")
        self.SY_clean_5.setGeometry(QRect(270, 350, 111, 41))
        self.SY_out_5 = QPushButton(self.SY_groupBox_3)
        self.SY_out_5.setObjectName(u"SY_out_5")
        self.SY_out_5.setGeometry(QRect(160, 350, 111, 41))
        self.SY_radioButton_TH_3 = QRadioButton(self.SY_groupBox_3)
        self.SY_radioButton_TH_3.setObjectName(u"SY_radioButton_TH_3")
        self.SY_radioButton_TH_3.setGeometry(QRect(60, 450, 98, 19))
        self.SY_radioButton_TH_3.setChecked(True)
        self.SY_radioButton_ZD_3 = QRadioButton(self.SY_groupBox_3)
        self.SY_radioButton_ZD_3.setObjectName(u"SY_radioButton_ZD_3")
        self.SY_radioButton_ZD_3.setGeometry(QRect(220, 450, 98, 19))
        self.SY_radioButton_ZD_3.setChecked(False)
        self.SY_widget_SC_3 = QWidget(self.SY_groupBox_3)
        self.SY_widget_SC_3.setObjectName(u"SY_widget_SC_3")
        self.SY_widget_SC_3.setGeometry(QRect(40, 500, 501, 81))
        self.SY_where_5 = QPushButton(self.SY_widget_SC_3)
        self.SY_where_5.setObjectName(u"SY_where_5")
        self.SY_where_5.setGeometry(QRect(380, 30, 111, 31))
        palette26 = QPalette()
        palette26.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush1)
        palette26.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Light, brush2)
        palette26.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Midlight, brush2)
        palette26.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush1)
        palette26.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Light, brush2)
        palette26.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Midlight, brush2)
        palette26.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush1)
        palette26.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Light, brush2)
        palette26.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Midlight, brush2)
        self.SY_where_5.setPalette(palette26)
        self.SY_outname_5 = QLabel(self.SY_widget_SC_3)
        self.SY_outname_5.setObjectName(u"SY_outname_5")
        self.SY_outname_5.setGeometry(QRect(20, 10, 91, 16))
        self.SY_lineEdit_5 = QLineEdit(self.SY_widget_SC_3)
        self.SY_lineEdit_5.setObjectName(u"SY_lineEdit_5")
        self.SY_lineEdit_5.setGeometry(QRect(20, 30, 321, 31))
        self.SY_label_2 = QLabel(self.SY_groupBox_3)
        self.SY_label_2.setObjectName(u"SY_label_2")
        self.SY_label_2.setGeometry(QRect(60, 610, 451, 111))
        self.SY_label_2.setFont(font5)
        self.SY_label_12 = QLabel(self.SY_groupBox_3)
        self.SY_label_12.setObjectName(u"SY_label_12")
        self.SY_label_12.setGeometry(QRect(60, 400, 201, 31))
        palette27 = QPalette()
        palette27.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.WindowText, brush4)
        palette27.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Text, brush5)
        palette27.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.ToolTipText, brush5)
        palette27.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.WindowText, brush4)
        palette27.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Text, brush5)
        palette27.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.ToolTipText, brush5)
        palette27.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.ToolTipText, brush5)
        self.SY_label_12.setPalette(palette27)
        self.SY_label_12.setContextMenuPolicy(Qt.ContextMenuPolicy.ActionsContextMenu)
        self.SY_label_12.setWordWrap(True)
        self.SY_groupBox_3KS_2 = QGroupBox(self.tab_SY)
        self.SY_groupBox_3KS_2.setObjectName(u"SY_groupBox_3KS_2")
        self.SY_groupBox_3KS_2.setGeometry(QRect(580, 290, 561, 481))
        self.SY_label_4RZ_2 = QLabel(self.SY_groupBox_3KS_2)
        self.SY_label_4RZ_2.setObjectName(u"SY_label_4RZ_2")
        self.SY_label_4RZ_2.setGeometry(QRect(20, 40, 81, 16))
        self.SY_label_4RZ_2.setFont(font3)
        self.SY_plainTextEdit_3RZ_2 = QPlainTextEdit(self.SY_groupBox_3KS_2)
        self.SY_plainTextEdit_3RZ_2.setObjectName(u"SY_plainTextEdit_3RZ_2")
        self.SY_plainTextEdit_3RZ_2.setGeometry(QRect(20, 60, 511, 301))
        self.SY_pushButton_5 = QPushButton(self.SY_groupBox_3KS_2)
        self.SY_pushButton_5.setObjectName(u"SY_pushButton_5")
        self.SY_pushButton_5.setGeometry(QRect(110, 420, 201, 41))
        palette28 = QPalette()
        palette28.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush6)
        palette28.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush6)
        palette28.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush6)
        self.SY_pushButton_5.setPalette(palette28)
        self.SY_pushButton_5.setFont(font2)
        self.SY_pushButton_6 = QPushButton(self.SY_groupBox_3KS_2)
        self.SY_pushButton_6.setObjectName(u"SY_pushButton_6")
        self.SY_pushButton_6.setGeometry(QRect(330, 420, 191, 41))
        palette29 = QPalette()
        palette29.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush7)
        palette29.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush7)
        palette29.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush7)
        self.SY_pushButton_6.setPalette(palette29)
        self.SY_pushButton_6.setFont(font2)
        self.SY_progressBar_4 = QProgressBar(self.SY_groupBox_3KS_2)
        self.SY_progressBar_4.setObjectName(u"SY_progressBar_4")
        self.SY_progressBar_4.setGeometry(QRect(150, 380, 381, 23))
        self.SY_progressBar_4.setFont(font4)
        self.SY_progressBar_4.setValue(0)
        self.SY_groupBox_sy = QGroupBox(self.tab_SY)
        self.SY_groupBox_sy.setObjectName(u"SY_groupBox_sy")
        self.SY_groupBox_sy.setGeometry(QRect(580, 20, 561, 251))
        self.line_5 = QFrame(self.SY_groupBox_sy)
        self.line_5.setObjectName(u"line_5")
        self.line_5.setGeometry(QRect(260, 30, 20, 201))
        self.line_5.setFrameShape(QFrame.Shape.VLine)
        self.line_5.setFrameShadow(QFrame.Shadow.Sunken)
        self.SY_radioButton_ZD = QRadioButton(self.SY_groupBox_sy)
        self.SY_radioButton_ZD.setObjectName(u"SY_radioButton_ZD")
        self.SY_radioButton_ZD.setGeometry(QRect(300, 30, 221, 21))
        self.SY_radioButton_ZD.setFont(font3)
        self.SY_radioButton_HHLOGO = QRadioButton(self.SY_groupBox_sy)
        self.SY_radioButton_HHLOGO.setObjectName(u"SY_radioButton_HHLOGO")
        self.SY_radioButton_HHLOGO.setGeometry(QRect(50, 70, 111, 19))
        self.SY_radioButton_HHTPSJ = QRadioButton(self.SY_groupBox_sy)
        self.SY_radioButton_HHTPSJ.setObjectName(u"SY_radioButton_HHTPSJ")
        self.SY_radioButton_HHTPSJ.setGeometry(QRect(50, 110, 101, 19))
        self.SY_radioButton_HHlogoJB = QRadioButton(self.SY_groupBox_sy)
        self.SY_radioButton_HHlogoJB.setObjectName(u"SY_radioButton_HHlogoJB")
        self.SY_radioButton_HHlogoJB.setGeometry(QRect(50, 150, 161, 19))
        self.radioButton_LUP = QRadioButton(self.SY_groupBox_sy)
        self.radioButton_LUP.setObjectName(u"radioButton_LUP")
        self.radioButton_LUP.setGeometry(QRect(100, 180, 51, 19))
        self.radioButton_RUP = QRadioButton(self.SY_groupBox_sy)
        self.radioButton_RUP.setObjectName(u"radioButton_RUP")
        self.radioButton_RUP.setGeometry(QRect(160, 180, 51, 19))
        self.radioButton_RDW = QRadioButton(self.SY_groupBox_sy)
        self.radioButton_RDW.setObjectName(u"radioButton_RDW")
        self.radioButton_RDW.setGeometry(QRect(160, 210, 51, 19))
        self.radioButton_LDW = QRadioButton(self.SY_groupBox_sy)
        self.radioButton_LDW.setObjectName(u"radioButton_LDW")
        self.radioButton_LDW.setGeometry(QRect(100, 210, 51, 19))
        self.SY_where_6ZD = QPushButton(self.SY_groupBox_sy)
        self.SY_where_6ZD.setObjectName(u"SY_where_6ZD")
        self.SY_where_6ZD.setGeometry(QRect(460, 80, 81, 31))
        palette30 = QPalette()
        palette30.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Button, brush1)
        palette30.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Light, brush2)
        palette30.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Midlight, brush2)
        palette30.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Button, brush1)
        palette30.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Light, brush2)
        palette30.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Midlight, brush2)
        palette30.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Button, brush1)
        palette30.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Light, brush2)
        palette30.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Midlight, brush2)
        self.SY_where_6ZD.setPalette(palette30)
        self.SY_lineEdit_6ZD = QLineEdit(self.SY_groupBox_sy)
        self.SY_lineEdit_6ZD.setObjectName(u"SY_lineEdit_6ZD")
        self.SY_lineEdit_6ZD.setGeometry(QRect(290, 80, 151, 31))
        self.SY_spinBox_ZDSZ = QSpinBox(self.SY_groupBox_sy)
        self.SY_spinBox_ZDSZ.setObjectName(u"SY_spinBox_ZDSZ")
        self.SY_spinBox_ZDSZ.setGeometry(QRect(420, 150, 91, 31))
        self.SY_spinBox_ZDSZ.setMinimum(0)
        self.SY_spinBox_ZDSZ.setMaximum(100)
        self.SY_spinBox_ZDSZ.setValue(10)
        self.SY_label_ZDZD = QLabel(self.SY_groupBox_sy)
        self.SY_label_ZDZD.setObjectName(u"SY_label_ZDZD")
        self.SY_label_ZDZD.setGeometry(QRect(300, 160, 101, 21))
        self.SY_label_11 = QLabel(self.SY_groupBox_sy)
        self.SY_label_11.setObjectName(u"SY_label_11")
        self.SY_label_11.setGeometry(QRect(290, 200, 201, 31))
        palette31 = QPalette()
        palette31.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.WindowText, brush4)
        palette31.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.Text, brush5)
        palette31.setBrush(QPalette.ColorGroup.Active, QPalette.ColorRole.ToolTipText, brush5)
        palette31.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.WindowText, brush4)
        palette31.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.Text, brush5)
        palette31.setBrush(QPalette.ColorGroup.Inactive, QPalette.ColorRole.ToolTipText, brush5)
        palette31.setBrush(QPalette.ColorGroup.Disabled, QPalette.ColorRole.ToolTipText, brush5)
        self.SY_label_11.setPalette(palette31)
        self.SY_label_11.setContextMenuPolicy(Qt.ContextMenuPolicy.ActionsContextMenu)
        self.SY_label_11.setWordWrap(True)
        self.SY_radioButton_HHLOGO_2 = QRadioButton(self.SY_groupBox_sy)
        self.SY_radioButton_HHLOGO_2.setObjectName(u"SY_radioButton_HHLOGO_2")
        self.SY_radioButton_HHLOGO_2.setGeometry(QRect(50, 30, 111, 19))
        self.SY_spinBox_ZDSZ_2 = QSpinBox(self.SY_groupBox_sy)
        self.SY_spinBox_ZDSZ_2.setObjectName(u"SY_spinBox_ZDSZ_2")
        self.SY_spinBox_ZDSZ_2.setGeometry(QRect(170, 20, 71, 31))
        self.SY_spinBox_ZDSZ_2.setMinimum(0)
        self.SY_spinBox_ZDSZ_2.setMaximum(100)
        self.SY_spinBox_ZDSZ_2.setValue(15)
        self.SY_spinBox_ZDSZ_3 = QSpinBox(self.SY_groupBox_sy)
        self.SY_spinBox_ZDSZ_3.setObjectName(u"SY_spinBox_ZDSZ_3")
        self.SY_spinBox_ZDSZ_3.setGeometry(QRect(170, 60, 71, 31))
        self.SY_spinBox_ZDSZ_3.setMinimum(0)
        self.SY_spinBox_ZDSZ_3.setMaximum(100)
        self.SY_spinBox_ZDSZ_3.setValue(6)
        self.SY_spinBox_ZDSZ_4 = QSpinBox(self.SY_groupBox_sy)
        self.SY_spinBox_ZDSZ_4.setObjectName(u"SY_spinBox_ZDSZ_4")
        self.SY_spinBox_ZDSZ_4.setGeometry(QRect(170, 100, 71, 31))
        self.SY_spinBox_ZDSZ_4.setMinimum(0)
        self.SY_spinBox_ZDSZ_4.setMaximum(100)
        self.SY_spinBox_ZDSZ_4.setValue(8)
        self.checkBox_AI = QCheckBox(self.SY_groupBox_sy)
        self.checkBox_AI.setObjectName(u"checkBox_AI")
        self.checkBox_AI.setGeometry(QRect(10, 220, 71, 19))
        self.checkBox_AI.setFont(font4)
        self.SY_radioButton_nothing = QRadioButton(self.SY_groupBox_sy)
        self.SY_radioButton_nothing.setObjectName(u"SY_radioButton_nothing")
        self.SY_radioButton_nothing.setGeometry(QRect(10, 190, 51, 19))
        self.HJ_tab.addTab(self.tab_SY, "")
        self.checkBox = QCheckBox(self.centralwidget)
        self.checkBox.setObjectName(u"checkBox")
        self.checkBox.setGeometry(QRect(1080, 0, 83, 19))
        mainWindow.setCentralWidget(self.centralwidget)
        self.statusbar = QStatusBar(mainWindow)
        self.statusbar.setObjectName(u"statusbar")
        mainWindow.setStatusBar(self.statusbar)
        self.menubar = QMenuBar(mainWindow)
        self.menubar.setObjectName(u"menubar")
        self.menubar.setGeometry(QRect(0, 0, 1186, 33))
        self.menuMFChen_v0_1 = QMenu(self.menubar)
        self.menuMFChen_v0_1.setObjectName(u"menuMFChen_v0_1")
        mainWindow.setMenuBar(self.menubar)

        self.menubar.addAction(self.menuMFChen_v0_1.menuAction())

        self.retranslateUi(mainWindow)

        self.HJ_tab.setCurrentIndex(0)


        QMetaObject.connectSlotsByName(mainWindow)
    # setupUi

    def retranslateUi(self, mainWindow):
        mainWindow.setWindowTitle(QCoreApplication.translate("mainWindow", u"\u5b5f\u526a\u5de5\u5177", None))
        self.SPZD_groupBox.setTitle(QCoreApplication.translate("mainWindow", u"\u89c6\u9891\u6307\u5b9a\u533a", None))
        self.QT_name.setText(QCoreApplication.translate("mainWindow", u"\u524d\u8d34\u7d20\u6750\u5217\u8868", None))
        self.QT_in.setText(QCoreApplication.translate("mainWindow", u"\u6dfb\u52a0\u524d\u8d34\u6587\u4ef6", None))
        self.QT_out.setText(QCoreApplication.translate("mainWindow", u"\u79fb\u9664\u9009\u4e2d", None))
        self.QT_clean.setText(QCoreApplication.translate("mainWindow", u"\u6e05\u7a7a\u5217\u8868", None))
        self.HT_name_2.setText(QCoreApplication.translate("mainWindow", u"\u540e\u8d34\u7d20\u6750\u5217\u8868", None))
        self.HT_out.setText(QCoreApplication.translate("mainWindow", u"\u79fb\u9664\u9009\u4e2d", None))
        self.HT_clean.setText(QCoreApplication.translate("mainWindow", u"\u6e05\u7a7a\u5217\u8868", None))
        self.HT_in.setText(QCoreApplication.translate("mainWindow", u"\u6dfb\u52a0\u540e\u8d34\u6587\u4ef6", None))
        self.SC_name.setText(QCoreApplication.translate("mainWindow", u"\u8f93\u51fa\u76ee\u5f55\uff1a", None))
        self.SC_where.setText(QCoreApplication.translate("mainWindow", u"\u9009\u62e9\u8f93\u51fa\u76ee\u5f55", None))
        self.CSHJC.setText(QCoreApplication.translate("mainWindow", u"\u521d\u59cb\u5316\u68c0\u67e5", None))
        self.KZ_groupBox.setTitle(QCoreApplication.translate("mainWindow", u"\u6269\u5c55\u529f\u80fd", None))
        self.CZ_checkBox.setText(QCoreApplication.translate("mainWindow", u"\u62bd\u5e27\u53bb\u91cd", None))
        self.CZ_label.setText(QCoreApplication.translate("mainWindow", u"\u6700\u5c0f\u62bd\u5e27\u6570\uff1a", None))
        self.CZ_label_2.setText(QCoreApplication.translate("mainWindow", u"\u6700\u5927\u62bd\u5e27\u6570\uff1a", None))
        self.ZC_checkBox.setText(QCoreApplication.translate("mainWindow", u"\u52a0\u8f6c\u573a", None))
        self.CMM_checkBox_2.setText(QCoreApplication.translate("mainWindow", u"\u6279\u91cf\u91cd\u547d\u540d", None))
        self.CMM_label.setText(QCoreApplication.translate("mainWindow", u"\u8d77\u59cb\u503c\uff1a", None))
        self.CMM_label_2.setText(QCoreApplication.translate("mainWindow", u"\u540d\u79f0\u524d\u7f00\uff1a", None))
        self.CMM_label_3.setText(QCoreApplication.translate("mainWindow", u"\u540d\u79f0\u540e\u7f00\uff1a", None))
        self.CMM_checkBox_3.setText(QCoreApplication.translate("mainWindow", u"\u586b\u51450\u6765\u8865\u8db3\u957f\u5ea6", None))
        self.YY_checkBox_2.setText(QCoreApplication.translate("mainWindow", u"\u6574\u4f53\u6dfb\u52a0\u80cc\u666f\u97f3\u4e50", None))
        self.YY_pushButton.setText(QCoreApplication.translate("mainWindow", u"\u9009\u62e9\u97f3\u4e50\u76ee\u5f55", None))
        self.YY_label.setText(QCoreApplication.translate("mainWindow", u"\u539f\u89c6\u9891\u97f3\u91cf\uff1a", None))
        self.YY_label_2.setText(QCoreApplication.translate("mainWindow", u"\u97f3\u4e50\u97f3\u91cf\uff1a", None))
        self.YY_label_3.setText(QCoreApplication.translate("mainWindow", u"dB", None))
        self.YY_label_4.setText(QCoreApplication.translate("mainWindow", u"dB", None))
        self.YY_label_5.setText(QCoreApplication.translate("mainWindow", u"\u6ce8\uff1a\u97f3\u91cf\u8303\u56f4\u4e3a-30dB\u523015dB", None))
        self.ZC_comboBox.setItemText(0, QCoreApplication.translate("mainWindow", u"\u3010\u3010\u5168\u968f\u673a\u3011\u3011", None))
        self.ZC_comboBox.setItemText(1, QCoreApplication.translate("mainWindow", u"\u3010\u3010\u67d4 \u00b7 \u968f\u673a\u3011\u3011", None))
        self.ZC_comboBox.setItemText(2, QCoreApplication.translate("mainWindow", u"\u3010\u3010\u786c \u00b7 \u968f\u673a\u3011\u3011", None))
        self.ZC_comboBox.setItemText(3, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u53e0\u5316\u3011fade", None))
        self.ZC_comboBox.setItemText(4, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u5de6\u64e6\u9664\u3011wipeleft", None))
        self.ZC_comboBox.setItemText(5, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u53f3\u64e6\u9664\u3011wiperight", None))
        self.ZC_comboBox.setItemText(6, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u4e0a\u64e6\u9664\u3011wipeup", None))
        self.ZC_comboBox.setItemText(7, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u4e0b\u64e6\u9664\u3011wipedown", None))
        self.ZC_comboBox.setItemText(8, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5411\u5de6\u64e6\u9664\u3011smoothleft", None))
        self.ZC_comboBox.setItemText(9, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5411\u53f3\u64e6\u9664\u3011smoothright", None))
        self.ZC_comboBox.setItemText(10, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5411\u4e0a\u64e6\u9664\u3011smoothup", None))
        self.ZC_comboBox.setItemText(11, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5411\u4e0b\u64e6\u9664\u3011smoothdown", None))
        self.ZC_comboBox.setItemText(12, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u5de6\u6ed1\u52a8\u3011slideleft", None))
        self.ZC_comboBox.setItemText(13, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u53f3\u6ed1\u52a8\u3011slideright", None))
        self.ZC_comboBox.setItemText(14, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u4e0a\u6ed1\u52a8\u3011slideup", None))
        self.ZC_comboBox.setItemText(15, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u4e0b\u6ed1\u52a8\u3011slidedown", None))
        self.ZC_comboBox.setItemText(16, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5706\u5f62\u5c55\u5f00\u3011circleopen", None))
        self.ZC_comboBox.setItemText(17, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5706\u5f62\u95ed\u5408\u3011circleclose", None))
        self.ZC_comboBox.setItemText(18, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5782\u76f4\u5c55\u5f00\u3011vertopen", None))
        self.ZC_comboBox.setItemText(19, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5782\u76f4\u95ed\u5408\u3011vertclose", None))
        self.ZC_comboBox.setItemText(20, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u6c34\u5e73\u5c55\u5f00\u3011horzopen", None))
        self.ZC_comboBox.setItemText(21, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u6c34\u5e73\u95ed\u5408\u3011horzclose", None))
        self.ZC_comboBox.setItemText(22, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u666f\u6df1\u8f6c\u573a\u3011distance", None))
        self.ZC_comboBox.setItemText(23, QCoreApplication.translate("mainWindow", u"\u3010\u65f6\u949f\u64e6\u9664\u3011radial", None))
        self.ZC_comboBox.setItemText(24, QCoreApplication.translate("mainWindow", u"\u3010\u50cf\u7d20\u6a21\u7cca\u3011pixelize", None))
        self.ZC_comboBox.setItemText(25, QCoreApplication.translate("mainWindow", u"\u3010\u653e\u5927\u8f6c\u573a\u3011zoomin", None))
        self.ZC_comboBox.setItemText(26, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5411\u5de6\u4e0a\u64e6\u9664\u3011diagtl", None))
        self.ZC_comboBox.setItemText(27, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5411\u53f3\u4e0a\u64e6\u9664\u3011diagtr", None))
        self.ZC_comboBox.setItemText(28, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5411\u5de6\u4e0b\u64e6\u9664\u3011diagbl", None))
        self.ZC_comboBox.setItemText(29, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5411\u53f3\u4e0b\u64e6\u9664\u3011diagbr", None))
        self.ZC_comboBox.setItemText(30, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u5de6\u4e0a\u64e6\u9664\u3011wipetl", None))
        self.ZC_comboBox.setItemText(31, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u53f3\u4e0a\u64e6\u9664\u3011wipetr", None))
        self.ZC_comboBox.setItemText(32, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u5de6\u4e0b\u64e6\u9664\u3011wipebl", None))
        self.ZC_comboBox.setItemText(33, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u53f3\u4e0b\u64e6\u9664\u3011wipebr", None))
        self.ZC_comboBox.setItemText(34, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u5de6\u767e\u53f6\u7a97\u3011hlslice", None))
        self.ZC_comboBox.setItemText(35, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u53f3\u767e\u53f6\u7a97\u3011hrslice", None))
        self.ZC_comboBox.setItemText(36, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u4e0a\u767e\u53f6\u7a97\u3011vuslice", None))
        self.ZC_comboBox.setItemText(37, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u4e0b\u767e\u53f6\u7a97\u3011vdslice", None))
        self.ZC_comboBox.setItemText(38, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u5de6\u6ed1\u523a\u3011hlwind", None))
        self.ZC_comboBox.setItemText(39, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u53f3\u6ed1\u523a\u3011hrwind", None))
        self.ZC_comboBox.setItemText(40, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u4e0a\u6ed1\u523a\u3011vuwind", None))
        self.ZC_comboBox.setItemText(41, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u4e0b\u6ed1\u523a\u3011vdwind", None))

        self.ZC_label_10.setText(QCoreApplication.translate("mainWindow", u"\u8f6c\u573a\u65f6\u957f", None))
        self.HX_groupBox.setTitle(QCoreApplication.translate("mainWindow", u"\u6838\u5fc3\u5904\u7406", None))
        self.CL_label.setText(QCoreApplication.translate("mainWindow", u"\u5904\u7406\u65e5\u5fd7", None))
        self.CL_label_2.setText(QCoreApplication.translate("mainWindow", u"\u7b56\u7565\u9009\u62e9", None))
        self.KS_pushButton.setText(QCoreApplication.translate("mainWindow", u"\u8ba9\u6211\u4eec\u55e8\u8d77\u6765\u597d\u561b", None))
        self.TZ_pushButton.setText(QCoreApplication.translate("mainWindow", u"\u505c\u4e0b\u6765 \u505c\u4e0b\u6765", None))
        self.HJ_tab.setTabText(self.HJ_tab.indexOf(self.tab_QT), QCoreApplication.translate("mainWindow", u"\u524d\u540e\u8d34\u5904\u7406\u5de5\u5177", None))
        self.DDCZ_pushButton_2.setText(QCoreApplication.translate("mainWindow", u"\u5f00\u59cb\u5427", None))
        self.DDCZ_pushButton_3.setText(QCoreApplication.translate("mainWindow", u"\u505c\u4e0b\u6765 \u505c\u4e0b\u6765", None))
        self.DDCZ_groupBox_KZ.setTitle(QCoreApplication.translate("mainWindow", u"\u6269\u5c55\u5de5\u5177", None))
        self.CMM_checkBox_5.setText(QCoreApplication.translate("mainWindow", u"\u586b\u51450\u6765\u8865\u8db3\u957f\u5ea6", None))
        self.CMM_label_6.setText(QCoreApplication.translate("mainWindow", u"\u540d\u79f0\u540e\u7f00\uff1a", None))
        self.CMM_checkBox_4.setText(QCoreApplication.translate("mainWindow", u"\u6279\u91cf\u91cd\u547d\u540d", None))
        self.CMM_label_4.setText(QCoreApplication.translate("mainWindow", u"\u540d\u79f0\u524d\u7f00\uff1a", None))
        self.CMM_label_5.setText(QCoreApplication.translate("mainWindow", u"\u8d77\u59cb\u503c\uff1a", None))
        self.DDCZ_groupBox.setTitle(QCoreApplication.translate("mainWindow", u"\u62bd\u5e27\u8bbe\u5b9a\u533a", None))
        self.DDCZ_name_3.setText(QCoreApplication.translate("mainWindow", u"\u5355\u72ec\u62bd\u5e27\u6587\u4ef6", None))
        self.DDCZ_label_3.setText(QCoreApplication.translate("mainWindow", u"\u6700\u5c0f\u62bd\u5e27\u6570\uff1a", None))
        self.DDCZ_in_3.setText(QCoreApplication.translate("mainWindow", u"\u6dfb\u52a0\u6587\u4ef6", None))
        self.DDCZ_label_4.setText(QCoreApplication.translate("mainWindow", u"\u6700\u5927\u62bd\u5e27\u6570\uff1a", None))
        self.DDCZ_clean_3.setText(QCoreApplication.translate("mainWindow", u"\u6e05\u7a7a\u5217\u8868", None))
        self.DDCZ_out_3.setText(QCoreApplication.translate("mainWindow", u"\u79fb\u9664\u9009\u4e2d", None))
        self.DDCZ_radioButton_TH.setText(QCoreApplication.translate("mainWindow", u"\u66ff\u6362\u539f\u7d20\u6750", None))
        self.DDCZ_radioButton_ZD.setText(QCoreApplication.translate("mainWindow", u"\u6307\u5b9a\u8f93\u51fa\u76ee\u5f55", None))
        self.DDCZ_where_3.setText(QCoreApplication.translate("mainWindow", u"\u9009\u62e9\u8f93\u51fa\u76ee\u5f55", None))
        self.DDCZ_outname_3.setText(QCoreApplication.translate("mainWindow", u"\u8f93\u51fa\u76ee\u5f55\uff1a", None))
        self.DDCZ_label_5.setText(QCoreApplication.translate("mainWindow", u"\u5904\u7406\u65e5\u5fd7", None))
        self.HJ_tab.setTabText(self.HJ_tab.indexOf(self.tab_CZ), QCoreApplication.translate("mainWindow", u"\u5355\u72ec\u62bd\u5e27\u5de5\u5177", None))
        self.HJSPZD_groupBox_2.setTitle(QCoreApplication.translate("mainWindow", u"\u89c6\u9891\u6307\u5b9a\u533a", None))
        self.HJ_name_4.setText(QCoreApplication.translate("mainWindow", u"\u7d20\u6750\u5217\u8868", None))
        self.HJ_in_4.setText(QCoreApplication.translate("mainWindow", u"\u6dfb\u52a0\u89c6\u9891\u6587\u4ef6", None))
        self.HJ_out_4.setText(QCoreApplication.translate("mainWindow", u"\u79fb\u9664\u9009\u4e2d", None))
        self.HJ_clean_4.setText(QCoreApplication.translate("mainWindow", u"\u6e05\u7a7a\u5217\u8868", None))
        self.HJSC_name_4.setText(QCoreApplication.translate("mainWindow", u"\u8f93\u51fa\u76ee\u5f55\uff1a", None))
        self.HJSC_where_4.setText(QCoreApplication.translate("mainWindow", u"\u9009\u62e9\u8f93\u51fa\u76ee\u5f55", None))
        self.CSHJC_4.setText(QCoreApplication.translate("mainWindow", u"\u521d\u59cb\u5316\u68c0\u67e5", None))
        self.HJ_label.setText(QCoreApplication.translate("mainWindow", u"\u5e0c\u671b\u751f\u6210\u7684\u89c6\u9891\u6570\u91cf", None))
        self.HJ_pushButton_GJ.setText(QCoreApplication.translate("mainWindow", u"\u9ad8\u7ea7\u8bbe\u7f6e \u25bc", None))
        self.HJ_label_GJ.setText(QCoreApplication.translate("mainWindow", u"\u57fa\u7840\u8bbe\u5b9a\u4e3a\uff1a\u7247\u6bb5\u957f\u5ea63~5\u79d2\uff0c\u76ee\u6807\u89c6\u9891\u603b\u65f6\u957f15-31\u79d2\n"
"\u5355\u7d20\u6750\u4ec5\u4f7f\u7528\u4e00\u6b21\uff0c\u7d20\u6750\u58f0\u97f3\u4fdd\u7559", None))
        self.HJ_groupBox_GJ.setTitle(QCoreApplication.translate("mainWindow", u"\u9ad8\u7ea7\u8bbe\u7f6e", None))
        self.GJ_label_PDmin.setText(QCoreApplication.translate("mainWindow", u"\u7247\u6bb5\u6700\u77ed\u65f6\u957f(s)", None))
        self.GJ_label_PDmax.setText(QCoreApplication.translate("mainWindow", u"\u7247\u6bb5\u6700\u957f\u65f6\u957f(s)", None))
        self.GJ_label_ZSCmin.setText(QCoreApplication.translate("mainWindow", u"\u603b\u65f6\u957f\u6700\u77ed(s)", None))
        self.GJ_label_ZSCmax.setText(QCoreApplication.translate("mainWindow", u"\u603b\u65f6\u957f\u6700\u957f(s)", None))
        self.GJ_checkBox.setText(QCoreApplication.translate("mainWindow", u"\u52fe\u9009\u65f6 \u53ef\u91cd\u590d\u4f7f\u7528\u5355\u6761\u7d20\u6750", None))
        self.GJ_checkBox_2.setText(QCoreApplication.translate("mainWindow", u"\u52fe\u9009\u65f6 \u7d20\u6750\u6240\u6709\u58f0\u97f3\u9759\u97f3", None))
        self.HJHX_groupBox_2.setTitle(QCoreApplication.translate("mainWindow", u"\u6838\u5fc3\u5904\u7406", None))
        self.HJCL_label_3.setText(QCoreApplication.translate("mainWindow", u"\u5904\u7406\u65e5\u5fd7", None))
        self.HJKS_pushButton_2.setText(QCoreApplication.translate("mainWindow", u"\u4e0b\u9762\u662f\u89c1\u8bc1\u5947\u8ff9\u7684\u65f6\u523b", None))
        self.HJTZ_pushButton_2.setText(QCoreApplication.translate("mainWindow", u"\u505c\u4e0b\u6765 \u505c\u4e0b\u6765", None))
        self.HJ_groupBox_HJZC.setTitle(QCoreApplication.translate("mainWindow", u"\u8f6c\u573a\u8bbe\u5b9a", None))
        self.HJ_checkBox_ZC.setText(QCoreApplication.translate("mainWindow", u"\u52a0\u8f6c\u573a", None))
        self.HJ_comboBox_ZC.setItemText(0, QCoreApplication.translate("mainWindow", u"\u3010\u3010\u5168\u968f\u673a\u3011\u3011", None))
        self.HJ_comboBox_ZC.setItemText(1, QCoreApplication.translate("mainWindow", u"\u3010\u3010\u67d4 \u00b7 \u968f\u673a\u3011\u3011", None))
        self.HJ_comboBox_ZC.setItemText(2, QCoreApplication.translate("mainWindow", u"\u3010\u3010\u786c \u00b7 \u968f\u673a\u3011\u3011", None))
        self.HJ_comboBox_ZC.setItemText(3, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u53e0\u5316\u3011fade", None))
        self.HJ_comboBox_ZC.setItemText(4, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u5de6\u64e6\u9664\u3011wipeleft", None))
        self.HJ_comboBox_ZC.setItemText(5, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u53f3\u64e6\u9664\u3011wiperight", None))
        self.HJ_comboBox_ZC.setItemText(6, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u4e0a\u64e6\u9664\u3011wipeup", None))
        self.HJ_comboBox_ZC.setItemText(7, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u4e0b\u64e6\u9664\u3011wipedown", None))
        self.HJ_comboBox_ZC.setItemText(8, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5411\u5de6\u64e6\u9664\u3011smoothleft", None))
        self.HJ_comboBox_ZC.setItemText(9, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5411\u53f3\u64e6\u9664\u3011smoothright", None))
        self.HJ_comboBox_ZC.setItemText(10, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5411\u4e0a\u64e6\u9664\u3011smoothup", None))
        self.HJ_comboBox_ZC.setItemText(11, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5411\u4e0b\u64e6\u9664\u3011smoothdown", None))
        self.HJ_comboBox_ZC.setItemText(12, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u5de6\u6ed1\u52a8\u3011slideleft", None))
        self.HJ_comboBox_ZC.setItemText(13, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u53f3\u6ed1\u52a8\u3011slideright", None))
        self.HJ_comboBox_ZC.setItemText(14, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u4e0a\u6ed1\u52a8\u3011slideup", None))
        self.HJ_comboBox_ZC.setItemText(15, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u4e0b\u6ed1\u52a8\u3011slidedown", None))
        self.HJ_comboBox_ZC.setItemText(16, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5706\u5f62\u5c55\u5f00\u3011circleopen", None))
        self.HJ_comboBox_ZC.setItemText(17, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5706\u5f62\u95ed\u5408\u3011circleclose", None))
        self.HJ_comboBox_ZC.setItemText(18, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5782\u76f4\u5c55\u5f00\u3011vertopen", None))
        self.HJ_comboBox_ZC.setItemText(19, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5782\u76f4\u95ed\u5408\u3011vertclose", None))
        self.HJ_comboBox_ZC.setItemText(20, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u6c34\u5e73\u5c55\u5f00\u3011horzopen", None))
        self.HJ_comboBox_ZC.setItemText(21, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u6c34\u5e73\u95ed\u5408\u3011horzclose", None))
        self.HJ_comboBox_ZC.setItemText(22, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u666f\u6df1\u8f6c\u573a\u3011distance", None))
        self.HJ_comboBox_ZC.setItemText(23, QCoreApplication.translate("mainWindow", u"\u3010\u65f6\u949f\u64e6\u9664\u3011radial", None))
        self.HJ_comboBox_ZC.setItemText(24, QCoreApplication.translate("mainWindow", u"\u3010\u50cf\u7d20\u6a21\u7cca\u3011pixelize", None))
        self.HJ_comboBox_ZC.setItemText(25, QCoreApplication.translate("mainWindow", u"\u3010\u653e\u5927\u8f6c\u573a\u3011zoomin", None))
        self.HJ_comboBox_ZC.setItemText(26, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5411\u5de6\u4e0a\u64e6\u9664\u3011diagtl", None))
        self.HJ_comboBox_ZC.setItemText(27, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5411\u53f3\u4e0a\u64e6\u9664\u3011diagtr", None))
        self.HJ_comboBox_ZC.setItemText(28, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5411\u5de6\u4e0b\u64e6\u9664\u3011diagbl", None))
        self.HJ_comboBox_ZC.setItemText(29, QCoreApplication.translate("mainWindow", u"\u3010\u67d4\u3011\u3010\u5411\u53f3\u4e0b\u64e6\u9664\u3011diagbr", None))
        self.HJ_comboBox_ZC.setItemText(30, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u5de6\u4e0a\u64e6\u9664\u3011wipetl", None))
        self.HJ_comboBox_ZC.setItemText(31, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u53f3\u4e0a\u64e6\u9664\u3011wipetr", None))
        self.HJ_comboBox_ZC.setItemText(32, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u5de6\u4e0b\u64e6\u9664\u3011wipebl", None))
        self.HJ_comboBox_ZC.setItemText(33, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u53f3\u4e0b\u64e6\u9664\u3011wipebr", None))
        self.HJ_comboBox_ZC.setItemText(34, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u5de6\u767e\u53f6\u7a97\u3011hlslice", None))
        self.HJ_comboBox_ZC.setItemText(35, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u53f3\u767e\u53f6\u7a97\u3011hrslice", None))
        self.HJ_comboBox_ZC.setItemText(36, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u4e0a\u767e\u53f6\u7a97\u3011vuslice", None))
        self.HJ_comboBox_ZC.setItemText(37, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u4e0b\u767e\u53f6\u7a97\u3011vdslice", None))
        self.HJ_comboBox_ZC.setItemText(38, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u5de6\u6ed1\u523a\u3011hlwind", None))
        self.HJ_comboBox_ZC.setItemText(39, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u53f3\u6ed1\u523a\u3011hrwind", None))
        self.HJ_comboBox_ZC.setItemText(40, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u4e0a\u6ed1\u523a\u3011vuwind", None))
        self.HJ_comboBox_ZC.setItemText(41, QCoreApplication.translate("mainWindow", u"\u3010\u5411\u4e0b\u6ed1\u523a\u3011vdwind", None))

        self.HJ_radioButton_y.setText(QCoreApplication.translate("mainWindow", u"\u6bcf\u6bb5\u90fd\u968f\u673a", None))
        self.HJ_radioButton_n.setText(QCoreApplication.translate("mainWindow", u"\u53ea\u7528\u4e00\u4e2a\u8f6c\u573a", None))
        self.ZC_label_11.setText(QCoreApplication.translate("mainWindow", u"\u8f6c\u573a\u65f6\u957f(s)", None))
        self.KZ_groupBox_2.setTitle(QCoreApplication.translate("mainWindow", u"\u6269\u5c55\u529f\u80fd", None))
        self.CZ_checkBox_2.setText(QCoreApplication.translate("mainWindow", u"\u62bd\u5e27\u53bb\u91cd", None))
        self.CZ_label_3.setText(QCoreApplication.translate("mainWindow", u"\u6700\u5c0f\u62bd\u5e27\u6570\uff1a", None))
        self.CZ_label_4.setText(QCoreApplication.translate("mainWindow", u"\u6700\u5927\u62bd\u5e27\u6570\uff1a", None))
        self.CMM_checkBox_6.setText(QCoreApplication.translate("mainWindow", u"\u6279\u91cf\u91cd\u547d\u540d", None))
        self.CMM_label_7.setText(QCoreApplication.translate("mainWindow", u"\u8d77\u59cb\u503c\uff1a", None))
        self.CMM_label_8.setText(QCoreApplication.translate("mainWindow", u"\u540d\u79f0\u524d\u7f00\uff1a", None))
        self.CMM_label_9.setText(QCoreApplication.translate("mainWindow", u"\u540d\u79f0\u540e\u7f00\uff1a", None))
        self.CMM_checkBox_7.setText(QCoreApplication.translate("mainWindow", u"\u586b\u51450\u6765\u8865\u8db3\u957f\u5ea6", None))
        self.YY_checkBox_3.setText(QCoreApplication.translate("mainWindow", u"\u6574\u4f53\u6dfb\u52a0\u80cc\u666f\u97f3\u4e50", None))
        self.YY_pushButton_2.setText(QCoreApplication.translate("mainWindow", u"\u9009\u62e9\u97f3\u4e50\u76ee\u5f55", None))
        self.YY_label_6.setText(QCoreApplication.translate("mainWindow", u"\u539f\u89c6\u9891\u97f3\u91cf\uff1a", None))
        self.YY_label_7.setText(QCoreApplication.translate("mainWindow", u"\u97f3\u4e50\u97f3\u91cf\uff1a", None))
        self.YY_label_8.setText(QCoreApplication.translate("mainWindow", u"dB", None))
        self.YY_label_9.setText(QCoreApplication.translate("mainWindow", u"dB", None))
        self.YY_label_10.setText(QCoreApplication.translate("mainWindow", u"\u6ce8\uff1a\u97f3\u91cf\u8303\u56f4\u4e3a-30dB\u523015dB", None))
        self.HJ_groupBox_LK.setTitle(QCoreApplication.translate("mainWindow", u"\u843d\u6b3e\u8bbe\u5b9a", None))
        self.HJ_checkBox_LK.setText(QCoreApplication.translate("mainWindow", u"\u52a0\u843d\u6b3e", None))
        self.LK_pushButton_3.setText(QCoreApplication.translate("mainWindow", u"\u9009\u62e9\u843d\u6b3e\u76ee\u5f55", None))
        self.LK_radioButton_duo.setText(QCoreApplication.translate("mainWindow", u"\u591a\u4e2a\u968f\u673a", None))
        self.LK_radioButton_dan.setText(QCoreApplication.translate("mainWindow", u"\u5355\u4e2a", None))
        self.LK_in_5.setText(QCoreApplication.translate("mainWindow", u"\u6dfb\u52a0", None))
        self.LK_clean_5.setText(QCoreApplication.translate("mainWindow", u"\u6e05\u7a7a", None))
        self.LK_label.setText(QCoreApplication.translate("mainWindow", u"\u6ce8\uff1a\u843d\u6b3e\u4e0d\u8ba1\u5165\u6df7\u526a\u603b\u65f6\u957f", None))
        self.HJ_tab.setTabText(self.HJ_tab.indexOf(self.tab_HJ), QCoreApplication.translate("mainWindow", u"\u6df7\u526a\u5de5\u5177", None))
        self.XSC_groupBox_2.setTitle(QCoreApplication.translate("mainWindow", u"\u7d20\u6750\u8bbe\u5b9a\u533a", None))
        self.XSC_name_4.setText(QCoreApplication.translate("mainWindow", u"\u8981\u6d17\u7684\u6587\u4ef6", None))
        self.XSC_in_4.setText(QCoreApplication.translate("mainWindow", u"\u6dfb\u52a0\u6587\u4ef6", None))
        self.XSC_clean_4.setText(QCoreApplication.translate("mainWindow", u"\u6e05\u7a7a\u5217\u8868", None))
        self.XSC_out_4.setText(QCoreApplication.translate("mainWindow", u"\u79fb\u9664\u9009\u4e2d", None))
        self.XSC_radioButton_TH_2.setText(QCoreApplication.translate("mainWindow", u"\u66ff\u6362\u539f\u7d20\u6750", None))
        self.XSC_radioButton_ZD_2.setText(QCoreApplication.translate("mainWindow", u"\u6307\u5b9a\u8f93\u51fa\u76ee\u5f55", None))
        self.XSC_where_4.setText(QCoreApplication.translate("mainWindow", u"\u9009\u62e9\u8f93\u51fa\u76ee\u5f55", None))
        self.XSC_outname_4.setText(QCoreApplication.translate("mainWindow", u"\u8f93\u51fa\u76ee\u5f55\uff1a", None))
        self.XSC_label.setText(QCoreApplication.translate("mainWindow", u"\u3010\u66ff\u6362\u539f\u7d20\u6750\u3011\u65f6\u65e0\u6cd5\u9009\u62e9\u8f93\u51fa\u76ee\u5f55\uff0c\u76f4\u63a5\u5728\u6e90\u6587\u4ef6\u5939\u91cc\u8986\u76d6\u6389\u539f\u7d20\u6750\u4e86", None))
        self.XSC_groupBox_FBL.setTitle(QCoreApplication.translate("mainWindow", u"\u76ee\u6807\u5206\u8fa8\u7387", None))
        self.W_label.setText(QCoreApplication.translate("mainWindow", u"\u5bbd\u5ea6\uff1a", None))
        self.SXC_textEdit_W.setHtml(QCoreApplication.translate("mainWindow", u"<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"hr { height: 1px; border-width: 0; }\n"
"li.unchecked::marker { content: \"\\2610\"; }\n"
"li.checked::marker { content: \"\\2612\"; }\n"
"</style></head><body style=\" font-family:'Microsoft YaHei UI'; font-size:9pt; font-weight:400; font-style:normal;\">\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">1080</p></body></html>", None))
        self.H_label.setText(QCoreApplication.translate("mainWindow", u"\u9ad8\u5ea6\uff1a", None))
        self.SXC_textEdit_H.setHtml(QCoreApplication.translate("mainWindow", u"<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"hr { height: 1px; border-width: 0; }\n"
"li.unchecked::marker { content: \"\\2610\"; }\n"
"li.checked::marker { content: \"\\2612\"; }\n"
"</style></head><body style=\" font-family:'Microsoft YaHei UI'; font-size:9pt; font-weight:400; font-style:normal;\">\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">1920</p></body></html>", None))
        self.label.setText(QCoreApplication.translate("mainWindow", u"\u56fa\u5b9a30\u5e27", None))
        self.XSC_groupBox_BTL.setTitle(QCoreApplication.translate("mainWindow", u"\u76ee\u6807\u89c6\u9891\u6bd4\u7279\u7387\uff08kbps\uff09", None))
        self.BTL_label.setText(QCoreApplication.translate("mainWindow", u"\u5efa\u8bae\u503c\uff1a\u539f\u89c6\u9891\u6bd4\u7279\u7387\u5de6\u53f3\u5373\u53ef\uff0c\u65e0\u811112000\u4e5f\u884c", None))
        self.SXC_textEdit_BTL.setHtml(QCoreApplication.translate("mainWindow", u"<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"hr { height: 1px; border-width: 0; }\n"
"li.unchecked::marker { content: \"\\2610\"; }\n"
"li.checked::marker { content: \"\\2612\"; }\n"
"</style></head><body style=\" font-family:'Microsoft YaHei UI'; font-size:9pt; font-weight:400; font-style:normal;\">\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">12000</p></body></html>", None))
        self.XSC_groupBox_3KS.setTitle(QCoreApplication.translate("mainWindow", u"\u6838\u5fc3\u5904\u7406", None))
        self.XSC_label_4RZ.setText(QCoreApplication.translate("mainWindow", u"\u5904\u7406\u65e5\u5fd7", None))
        self.XSC_pushButton_3.setText(QCoreApplication.translate("mainWindow", u"\u5e9f\u8bdd\u5c11\u8bf4", None))
        self.XSC_pushButton_4.setText(QCoreApplication.translate("mainWindow", u"\u53ef\u80fd\u505c\u4e0d\u4e0b\u6765\u7684\u505c\u6b62\u6309\u94ae", None))
        self.HJ_tab.setTabText(self.HJ_tab.indexOf(self.XSC_tab), QCoreApplication.translate("mainWindow", u"\u6d17\u7d20\u6750", None))
        self.SY_groupBox_3.setTitle(QCoreApplication.translate("mainWindow", u"\u7d20\u6750\u8bbe\u5b9a\u533a", None))
        self.SY_name_5.setText(QCoreApplication.translate("mainWindow", u"\u9700\u8981\u6dfb\u52a0\u6c34\u5370\u7684\u89c6\u9891", None))
        self.SY_in_5.setText(QCoreApplication.translate("mainWindow", u"\u6dfb\u52a0\u6587\u4ef6", None))
        self.SY_clean_5.setText(QCoreApplication.translate("mainWindow", u"\u6e05\u7a7a\u5217\u8868", None))
        self.SY_out_5.setText(QCoreApplication.translate("mainWindow", u"\u79fb\u9664\u9009\u4e2d", None))
        self.SY_radioButton_TH_3.setText(QCoreApplication.translate("mainWindow", u"\u66ff\u6362\u539f\u7d20\u6750", None))
        self.SY_radioButton_ZD_3.setText(QCoreApplication.translate("mainWindow", u"\u6307\u5b9a\u8f93\u51fa\u76ee\u5f55", None))
        self.SY_where_5.setText(QCoreApplication.translate("mainWindow", u"\u9009\u62e9\u8f93\u51fa\u76ee\u5f55", None))
        self.SY_outname_5.setText(QCoreApplication.translate("mainWindow", u"\u8f93\u51fa\u76ee\u5f55\uff1a", None))
        self.SY_label_2.setText(QCoreApplication.translate("mainWindow", u"\u3010\u66ff\u6362\u539f\u7d20\u6750\u3011\u65f6\u65e0\u6cd5\u9009\u62e9\u8f93\u51fa\u76ee\u5f55\uff0c\u76f4\u63a5\u5728\u6e90\u6587\u4ef6\u5939\u91cc\u8986\u76d6\u6389\u539f\u7d20\u6750\u4e86", None))
        self.SY_label_12.setText(QCoreApplication.translate("mainWindow", u"\u6ce8\uff1a\u89c6\u9891\u75281080P\u7684", None))
        self.SY_groupBox_3KS_2.setTitle(QCoreApplication.translate("mainWindow", u"\u6838\u5fc3\u5904\u7406", None))
        self.SY_label_4RZ_2.setText(QCoreApplication.translate("mainWindow", u"\u5904\u7406\u65e5\u5fd7", None))
        self.SY_pushButton_5.setText(QCoreApplication.translate("mainWindow", u"\u9877\u523b\u70bc\u5316", None))
        self.SY_pushButton_6.setText(QCoreApplication.translate("mainWindow", u"\u54df\uff0c\u4f60\u5148\u7b49\u7b49", None))
        self.SY_groupBox_sy.setTitle(QCoreApplication.translate("mainWindow", u"\u6c34\u5370\u6307\u5b9a\u533a", None))
        self.SY_radioButton_ZD.setText(QCoreApplication.translate("mainWindow", u"\u81ea\u5b9a\u4e49 \uff08\u4ec5\u5168\u5c4f\u56fe\u7247\uff09", None))
        self.SY_radioButton_HHLOGO.setText(QCoreApplication.translate("mainWindow", u"\u5168\u5c4f\u5408\u5408LOGO", None))
        self.SY_radioButton_HHTPSJ.setText(QCoreApplication.translate("mainWindow", u"\u9884\u8bbe\u5168\u5c4f\u56fe\u7247", None))
        self.SY_radioButton_HHlogoJB.setText(QCoreApplication.translate("mainWindow", u"\u5408\u5408LOGO\u89d2\u6807  (100%)", None))
        self.radioButton_LUP.setText(QCoreApplication.translate("mainWindow", u"\u5de6\u4e0a", None))
        self.radioButton_RUP.setText(QCoreApplication.translate("mainWindow", u"\u53f3\u4e0a", None))
        self.radioButton_RDW.setText(QCoreApplication.translate("mainWindow", u"\u53f3\u4e0b", None))
        self.radioButton_LDW.setText(QCoreApplication.translate("mainWindow", u"\u5de6\u4e0b", None))
        self.SY_where_6ZD.setText(QCoreApplication.translate("mainWindow", u"\u9009\u62e9\u76ee\u5f55", None))
        self.SY_label_ZDZD.setText(QCoreApplication.translate("mainWindow", u"\u4e0d\u900f\u660e\u5ea6\uff08%\uff09", None))
        self.SY_label_11.setText(QCoreApplication.translate("mainWindow", u"\u6ce8\uff1a\u56fe\u7247\u5c3d\u91cf\u4e0e\u89c6\u9891\u5c3a\u5bf8\u4e00\u81f4", None))
        self.SY_radioButton_HHLOGO_2.setText(QCoreApplication.translate("mainWindow", u"\u52a8\u6001\u5408\u5408LOGO", None))
        self.checkBox_AI.setText(QCoreApplication.translate("mainWindow", u"AI\u6807\u8bc6", None))
        self.SY_radioButton_nothing.setText(QCoreApplication.translate("mainWindow", u"\u65e0", None))
        self.HJ_tab.setTabText(self.HJ_tab.indexOf(self.tab_SY), QCoreApplication.translate("mainWindow", u"\u6c34\u5370\u5de5\u5177", None))
        self.checkBox.setText(QCoreApplication.translate("mainWindow", u"\u754c\u9762\u53cd\u8272", None))
        self.menuMFChen_v0_1.setTitle(QCoreApplication.translate("mainWindow", u"MFChen\u89c6\u9891\u6df7\u526a\u5de5\u5177 v2.7", None))
    # retranslateUi

