(['E:\\000混剪文件夹\\可视化project-pyside6\\main.py'],
 ['E:\\000混剪文件夹\\可视化project-pyside6'],
 ['utils',
  'video_mixer',
  'transition_mixer',
  'frame_removal',
  'machine_code_verifier',
  'machine_code_detector',
  'time_validator',
  'machine_code_converter',
  'startup_optimizer',
  'authorization_config_generator',
  'resource_manager',
  'PySide6.QtCore',
  'PySide6.QtWidgets',
  'PySide6.QtGui'],
 [('C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 ['tkinter', 'matplotlib', 'numpy', 'scipy', 'pandas', '__main__'],
 [],
 False,
 {},
 0,
 [],
 [('photo\\HHlogo.png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\HHlogo.png',
   'DATA'),
  ('photo\\HHlogo整张水印.png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\HHlogo整张水印.png',
   'DATA'),
  ('photo\\others\\1 (1).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\1 (1).png',
   'DATA'),
  ('photo\\others\\1 (2).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\1 (2).png',
   'DATA'),
  ('photo\\others\\1 (3).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\1 (3).png',
   'DATA'),
  ('photo\\others\\1 (4).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\1 (4).png',
   'DATA'),
  ('photo\\others\\1 (5).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\1 (5).png',
   'DATA'),
  ('photo\\others\\1 (6).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\1 (6).png',
   'DATA'),
  ('photo\\others\\1 (7).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\1 (7).png',
   'DATA'),
  ('photo\\others\\2 (1).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (1).jpg',
   'DATA'),
  ('photo\\others\\2 (1).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (1).png',
   'DATA'),
  ('photo\\others\\2 (10).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (10).jpg',
   'DATA'),
  ('photo\\others\\2 (11).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (11).jpg',
   'DATA'),
  ('photo\\others\\2 (14).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (14).jpg',
   'DATA'),
  ('photo\\others\\2 (15).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (15).jpg',
   'DATA'),
  ('photo\\others\\2 (16).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (16).jpg',
   'DATA'),
  ('photo\\others\\2 (17).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (17).jpg',
   'DATA'),
  ('photo\\others\\2 (18).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (18).jpg',
   'DATA'),
  ('photo\\others\\2 (19).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (19).jpg',
   'DATA'),
  ('photo\\others\\2 (2).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (2).jpg',
   'DATA'),
  ('photo\\others\\2 (2).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (2).png',
   'DATA'),
  ('photo\\others\\2 (20).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (20).jpg',
   'DATA'),
  ('photo\\others\\2 (21).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (21).jpg',
   'DATA'),
  ('photo\\others\\2 (22).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (22).jpg',
   'DATA'),
  ('photo\\others\\2 (23).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (23).jpg',
   'DATA'),
  ('photo\\others\\2 (24).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (24).jpg',
   'DATA'),
  ('photo\\others\\2 (25).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (25).jpg',
   'DATA'),
  ('photo\\others\\2 (26).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (26).jpg',
   'DATA'),
  ('photo\\others\\2 (27).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (27).jpg',
   'DATA'),
  ('photo\\others\\2 (28).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (28).jpg',
   'DATA'),
  ('photo\\others\\2 (29).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (29).jpg',
   'DATA'),
  ('photo\\others\\2 (3).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (3).jpg',
   'DATA'),
  ('photo\\others\\2 (3).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (3).png',
   'DATA'),
  ('photo\\others\\2 (30).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (30).jpg',
   'DATA'),
  ('photo\\others\\2 (31).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (31).jpg',
   'DATA'),
  ('photo\\others\\2 (32).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (32).jpg',
   'DATA'),
  ('photo\\others\\2 (33).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (33).jpg',
   'DATA'),
  ('photo\\others\\2 (34).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (34).jpg',
   'DATA'),
  ('photo\\others\\2 (35).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (35).jpg',
   'DATA'),
  ('photo\\others\\2 (36).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (36).jpg',
   'DATA'),
  ('photo\\others\\2 (4).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (4).jpg',
   'DATA'),
  ('photo\\others\\2 (4).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (4).png',
   'DATA'),
  ('photo\\others\\2 (5).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (5).jpg',
   'DATA'),
  ('photo\\others\\2 (5).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (5).png',
   'DATA'),
  ('photo\\others\\2 (6).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (6).jpg',
   'DATA'),
  ('photo\\others\\2 (7).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (7).jpg',
   'DATA'),
  ('photo\\others\\2 (8).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (8).jpg',
   'DATA'),
  ('photo\\others\\2 (9).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (9).jpg',
   'DATA'),
  ('photo\\others\\3.png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\3.png',
   'DATA'),
  ('ui_main_window.py',
   'E:\\000混剪文件夹\\可视化project-pyside6\\ui_main_window.py',
   'DATA')],
 '3.12.0 (tags/v3.12.0:0fb18b0, Oct  2 2023, 13:03:39) [MSC v.1935 64 bit '
 '(AMD64)]',
 [('pyi_rth_cryptography_openssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyside6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyside6.py',
   'PYSOURCE'),
  ('main', 'E:\\000混剪文件夹\\可视化project-pyside6\\main.py', 'PYSOURCE')],
 [('_pyi_rth_utils.qt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\typing.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tempfile.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\string.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\copy.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_strptime.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\calendar.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\argparse.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\gettext.py',
   'PYMODULE'),
  ('struct',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\struct.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\getopt.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\selectors.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\quopri.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\inspect.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\token.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\opcode.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ast.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\contextlib.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\textwrap.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile.__main__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zipfile\\__main__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\py_compile.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\lzma.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_compression.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\bz2.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\csv.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tokenize.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('authorization_config_generator',
   'E:\\000混剪文件夹\\可视化project-pyside6\\authorization_config_generator.py',
   'PYMODULE'),
  ('machine_code_converter',
   'E:\\000混剪文件夹\\可视化project-pyside6\\machine_code_converter.py',
   'PYMODULE'),
  ('time_validator',
   'E:\\000混剪文件夹\\可视化project-pyside6\\time_validator.py',
   'PYMODULE'),
  ('cryptography.fernet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\fernet.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.padding',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\__future__.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\hmac.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('machine_code_detector',
   'E:\\000混剪文件夹\\可视化project-pyside6\\machine_code_detector.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\platform.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\uuid.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\stringprep.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pprint.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('machine_code_verifier',
   'E:\\000混剪文件夹\\可视化project-pyside6\\machine_code_verifier.py',
   'PYMODULE'),
  ('portable_auth_verifier',
   'E:\\000混剪文件夹\\可视化project-pyside6\\portable_auth_verifier.py',
   'PYMODULE'),
  ('video_cleaner',
   'E:\\000混剪文件夹\\可视化project-pyside6\\video_cleaner.py',
   'PYMODULE'),
  ('frame_removal',
   'E:\\000混剪文件夹\\可视化project-pyside6\\frame_removal.py',
   'PYMODULE'),
  ('main', 'E:\\000混剪文件夹\\可视化project-pyside6\\main.py', 'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\gzip.py',
   'PYMODULE'),
  ('transition_mixer',
   'E:\\000混剪文件夹\\可视化project-pyside6\\transition_mixer.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\glob.py',
   'PYMODULE'),
  ('utils', 'E:\\000混剪文件夹\\可视化project-pyside6\\utils.py', 'PYMODULE'),
  ('resource_manager',
   'E:\\000混剪文件夹\\可视化project-pyside6\\resource_manager.py',
   'PYMODULE'),
  ('video_mixer',
   'E:\\000混剪文件夹\\可视化project-pyside6\\video_mixer.py',
   'PYMODULE'),
  ('ui_main_window',
   'E:\\000混剪文件夹\\可视化project-pyside6\\ui_main_window.py',
   'PYMODULE'),
  ('shiboken6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\shiboken6\\__init__.py',
   'PYMODULE'),
  ('PySide6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\__init__.py',
   'PYMODULE'),
  ('PySide6.support.deprecated',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\support\\deprecated.py',
   'PYMODULE'),
  ('PySide6.support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\support\\__init__.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pathlib.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\subprocess.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\signal.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\numbers.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\bisect.py',
   'PYMODULE'),
  ('startup_optimizer',
   'E:\\000混剪文件夹\\可视化project-pyside6\\startup_optimizer.py',
   'PYMODULE')],
 [('python312.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python312.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qpdf.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qdirect2d.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\platforms\\qdirect2d.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PySide6\\opengl32sw.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\opengl32sw.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qschannelbackend.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\tls\\qschannelbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qopensslbackend.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\tls\\qopensslbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\_cffi_backend.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('PySide6\\QtGui.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\QtGui.pyd',
   'EXTENSION'),
  ('shiboken6\\Shiboken.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\shiboken6\\Shiboken.pyd',
   'EXTENSION'),
  ('PySide6\\QtNetwork.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\QtNetwork.pyd',
   'EXTENSION'),
  ('PySide6\\QtCore.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\QtCore.pyd',
   'EXTENSION'),
  ('PySide6\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\QtWidgets.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('PySide6\\Qt6Core.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\Qt6Core.dll',
   'BINARY'),
  ('PySide6\\Qt6Gui.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\Qt6Gui.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('shiboken6\\MSVCP140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\shiboken6\\MSVCP140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PySide6\\Qt6Svg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\Qt6Svg.dll',
   'BINARY'),
  ('PySide6\\Qt6Network.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\Qt6Network.dll',
   'BINARY'),
  ('PySide6\\Qt6VirtualKeyboard.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\Qt6VirtualKeyboard.dll',
   'BINARY'),
  ('PySide6\\Qt6Pdf.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\Qt6Pdf.dll',
   'BINARY'),
  ('PySide6\\Qt6Widgets.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\Qt6Widgets.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python3.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('PySide6\\MSVCP140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\MSVCP140.dll',
   'BINARY'),
  ('PySide6\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PySide6\\pyside6.abi3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\pyside6.abi3.dll',
   'BINARY'),
  ('shiboken6\\shiboken6.abi3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\shiboken6\\shiboken6.abi3.dll',
   'BINARY'),
  ('PySide6\\MSVCP140_2.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\MSVCP140_2.dll',
   'BINARY'),
  ('PySide6\\VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\VCRUNTIME140.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\shiboken6\\VCRUNTIME140.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\shiboken6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\WINDOWS\\system32\\ucrtbase.dll', 'BINARY'),
  ('PySide6\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\MSVCP140_1.dll',
   'BINARY'),
  ('PySide6\\Qt6Quick.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\Qt6Quick.dll',
   'BINARY'),
  ('PySide6\\Qt6Qml.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\Qt6Qml.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('PySide6\\Qt6OpenGL.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\Qt6OpenGL.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlMeta.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\Qt6QmlMeta.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlModels.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\Qt6QmlModels.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlWorkerScript.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\Qt6QmlWorkerScript.dll',
   'BINARY')],
 [],
 [],
 [('photo\\HHlogo.png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\HHlogo.png',
   'DATA'),
  ('photo\\HHlogo整张水印.png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\HHlogo整张水印.png',
   'DATA'),
  ('photo\\others\\1 (1).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\1 (1).png',
   'DATA'),
  ('photo\\others\\1 (2).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\1 (2).png',
   'DATA'),
  ('photo\\others\\1 (3).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\1 (3).png',
   'DATA'),
  ('photo\\others\\1 (4).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\1 (4).png',
   'DATA'),
  ('photo\\others\\1 (5).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\1 (5).png',
   'DATA'),
  ('photo\\others\\1 (6).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\1 (6).png',
   'DATA'),
  ('photo\\others\\1 (7).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\1 (7).png',
   'DATA'),
  ('photo\\others\\2 (1).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (1).jpg',
   'DATA'),
  ('photo\\others\\2 (1).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (1).png',
   'DATA'),
  ('photo\\others\\2 (10).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (10).jpg',
   'DATA'),
  ('photo\\others\\2 (11).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (11).jpg',
   'DATA'),
  ('photo\\others\\2 (14).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (14).jpg',
   'DATA'),
  ('photo\\others\\2 (15).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (15).jpg',
   'DATA'),
  ('photo\\others\\2 (16).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (16).jpg',
   'DATA'),
  ('photo\\others\\2 (17).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (17).jpg',
   'DATA'),
  ('photo\\others\\2 (18).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (18).jpg',
   'DATA'),
  ('photo\\others\\2 (19).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (19).jpg',
   'DATA'),
  ('photo\\others\\2 (2).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (2).jpg',
   'DATA'),
  ('photo\\others\\2 (2).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (2).png',
   'DATA'),
  ('photo\\others\\2 (20).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (20).jpg',
   'DATA'),
  ('photo\\others\\2 (21).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (21).jpg',
   'DATA'),
  ('photo\\others\\2 (22).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (22).jpg',
   'DATA'),
  ('photo\\others\\2 (23).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (23).jpg',
   'DATA'),
  ('photo\\others\\2 (24).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (24).jpg',
   'DATA'),
  ('photo\\others\\2 (25).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (25).jpg',
   'DATA'),
  ('photo\\others\\2 (26).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (26).jpg',
   'DATA'),
  ('photo\\others\\2 (27).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (27).jpg',
   'DATA'),
  ('photo\\others\\2 (28).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (28).jpg',
   'DATA'),
  ('photo\\others\\2 (29).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (29).jpg',
   'DATA'),
  ('photo\\others\\2 (3).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (3).jpg',
   'DATA'),
  ('photo\\others\\2 (3).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (3).png',
   'DATA'),
  ('photo\\others\\2 (30).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (30).jpg',
   'DATA'),
  ('photo\\others\\2 (31).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (31).jpg',
   'DATA'),
  ('photo\\others\\2 (32).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (32).jpg',
   'DATA'),
  ('photo\\others\\2 (33).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (33).jpg',
   'DATA'),
  ('photo\\others\\2 (34).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (34).jpg',
   'DATA'),
  ('photo\\others\\2 (35).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (35).jpg',
   'DATA'),
  ('photo\\others\\2 (36).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (36).jpg',
   'DATA'),
  ('photo\\others\\2 (4).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (4).jpg',
   'DATA'),
  ('photo\\others\\2 (4).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (4).png',
   'DATA'),
  ('photo\\others\\2 (5).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (5).jpg',
   'DATA'),
  ('photo\\others\\2 (5).png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (5).png',
   'DATA'),
  ('photo\\others\\2 (6).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (6).jpg',
   'DATA'),
  ('photo\\others\\2 (7).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (7).jpg',
   'DATA'),
  ('photo\\others\\2 (8).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (8).jpg',
   'DATA'),
  ('photo\\others\\2 (9).jpg',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\2 (9).jpg',
   'DATA'),
  ('photo\\others\\3.png',
   'E:\\000混剪文件夹\\可视化project-pyside6\\photo\\others\\3.png',
   'DATA'),
  ('ui_main_window.py',
   'E:\\000混剪文件夹\\可视化project-pyside6\\ui_main_window.py',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('PySide6\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ka.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ka.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_sv.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nn.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fa.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nn.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ka.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pt_BR.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_BR.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_PT.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pt_BR.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_de.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_lt.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nn.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_he.qm',
   'DATA'),
  ('PySide6\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qt_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PySide6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('base_library.zip',
   'E:\\000混剪文件夹\\可视化project-pyside6\\build\\视频混剪工具\\base_library.zip',
   'DATA')])
