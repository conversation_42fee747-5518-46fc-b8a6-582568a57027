#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证AI标识水印功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_implementation():
    """验证实现"""
    print("=== 验证AI标识水印功能实现 ===")
    
    try:
        # 检查资源文件
        from resource_manager import get_photo_path
        
        ai_image_path = get_photo_path("AI生成.png")
        print(f"✅ AI图片路径: {ai_image_path}")
        
        if os.path.exists(ai_image_path):
            file_size = os.path.getsize(ai_image_path)
            print(f"✅ AI生成.png存在，大小: {file_size} 字节")
        else:
            print("❌ AI生成.png不存在")
            return False
        
        # 检查代码实现
        print("\n=== 检查代码实现 ===")
        
        # 读取main.py文件，检查关键代码
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键功能点
        checks = [
            ('checkBox_AI检查', 'checkBox_AI.isChecked()'),
            ('AI标识水印日志', '🤖 添加AI标识水印'),
            ('AI生成.png路径', 'get_photo_path("AI生成.png")'),
            ('100%不透明度', '1.0  # 100%不透明度'),
            ('AI水印成功日志', '✅ AI标识水印添加成功'),
        ]
        
        all_checks_passed = True
        for check_name, check_pattern in checks:
            if check_pattern in content:
                print(f"✅ {check_name}: 找到相关代码")
            else:
                print(f"❌ {check_name}: 未找到相关代码")
                all_checks_passed = False
        
        # 检查UI文件
        print("\n=== 检查UI文件 ===")
        
        with open('MFChen视频混剪工具.ui', 'r', encoding='utf-8') as f:
            ui_content = f.read()
        
        if 'name="checkBox_AI"' in ui_content:
            print("✅ UI文件中找到checkBox_AI定义")
        else:
            print("❌ UI文件中未找到checkBox_AI定义")
            all_checks_passed = False
        
        if 'AI标识' in ui_content:
            print("✅ UI文件中找到AI标识文本")
        else:
            print("❌ UI文件中未找到AI标识文本")
            all_checks_passed = False
        
        # 检查打包配置
        print("\n=== 检查打包配置 ===")
        
        spec_files = ['main.spec', '视频混剪工具.spec']
        spec_found = False
        
        for spec_file in spec_files:
            if os.path.exists(spec_file):
                with open(spec_file, 'r', encoding='utf-8') as f:
                    spec_content = f.read()
                
                if "('photo', 'photo')" in spec_content:
                    print(f"✅ {spec_file}中找到photo文件夹打包配置")
                    spec_found = True
                    break
        
        if not spec_found:
            print("❌ 未找到photo文件夹的打包配置")
            all_checks_passed = False
        
        return all_checks_passed
        
    except Exception as e:
        print(f"❌ 验证失败：{str(e)}")
        return False

def show_usage_instructions():
    """显示使用说明"""
    print("\n" + "="*60)
    print("AI标识水印功能使用说明")
    print("="*60)
    print()
    print("功能描述:")
    print("  在tab_SY水印工具中新增了【AI标识】复选框")
    print("  当勾选此复选框时，会在所有其他水印处理完成后")
    print("  再添加一个全屏AI标识水印")
    print()
    print("使用步骤:")
    print("  1. 在tab_SY中添加要处理的视频文件")
    print("  2. 选择任意一种水印类型（动态LOGO、全屏LOGO等）")
    print("  3. 勾选【AI标识】复选框")
    print("  4. 点击【顽刻炼化】开始处理")
    print()
    print("技术细节:")
    print("  - AI标识图片: photo/AI生成.png")
    print("  - 不透明度: 100%（完全不透明）")
    print("  - 覆盖方式: 全屏覆盖（1080x1920）")
    print("  - 处理顺序: 先处理选择的水印类型，再添加AI标识")
    print()
    print("打包说明:")
    print("  - AI生成.png文件会自动包含在打包的exe中")
    print("  - 无需额外配置，photo文件夹已在打包配置中")
    print()

def main():
    """主函数"""
    print("AI标识水印功能验证工具")
    print("="*40)
    
    # 验证实现
    implementation_ok = verify_implementation()
    
    # 显示结果
    print("\n" + "="*40)
    print("验证结果:")
    print("="*40)
    
    if implementation_ok:
        print("🎉 AI标识水印功能实现完整！")
        print("✅ 所有必要的代码和资源都已就位")
        print("✅ 可以正常使用此功能")
    else:
        print("⚠️ 发现一些问题，请检查上述输出")
    
    # 显示使用说明
    show_usage_instructions()

if __name__ == "__main__":
    main()
